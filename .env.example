# MongoDB Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password
MONGO_DATABASE=supermarket_data

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO

# Scraping Configuration
SCRAPING_INTERVAL=3600  # 1 hour in seconds
RATE_LIMIT_DELAY=2      # 2 seconds between requests
USER_AGENT_ROTATION=true
HEADLESS_BROWSER=true
MAX_RETRIES=3
TIMEOUT=30

# Scheduler Configuration
SCRAPING_SCHEDULE=0 */6 * * *  # Every 6 hours

# Redis Configuration (for caching and job queue)
REDIS_URL=redis://redis:6379

# Specific Scraper Settings
COTO_ENABLED=true
CARREFOUR_ENABLED=true
JUMBO_ENABLED=true
DISCO_ENABLED=true
VEA_ENABLED=true

# Proxy Configuration (optional)
USE_PROXY=false
PROXY_URL=
PROXY_USERNAME=
PROXY_PASSWORD=

# Notification Settings (optional)
ENABLE_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=
EMAIL_SMTP_SERVER=
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_RECIPIENTS=

# Data Retention
DATA_RETENTION_DAYS=30
CLEANUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
