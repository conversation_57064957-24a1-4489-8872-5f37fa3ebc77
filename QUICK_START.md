# Quick Start Guide

## ✅ System Status: OPERATIONAL

Your Argentine Supermarket Scraper is now fully deployed and running!

## 🚀 What's Running

- **API Server**: http://localhost:8000
- **MongoDB Database**: localhost:27017
- **Redis Cache**: localhost:6379
- **Scraper Service**: Running in background
- **Scheduler Service**: Running in background

## 📚 Access Points

### Interactive API Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### API Endpoints
- **Health Check**: http://localhost:8000/api/v1/health
- **Markets**: http://localhost:8000/api/v1/markets
- **Products**: http://localhost:8000/api/v1/products
- **Search**: http://localhost:8000/api/v1/products/search?q=coca%20cola

## 🧪 Test Commands

```bash
# Check system health
curl http://localhost:8000/api/v1/health

# List all markets
curl http://localhost:8000/api/v1/markets

# Get products (empty initially)
curl http://localhost:8000/api/v1/products

# Check service status
docker-compose ps

# View logs
docker-compose logs api
docker-compose logs scraper
```

## 🕷️ Running Scrapers

### Manual Scraping
```bash
# Run all scrapers
python scripts/run_scraper.py

# Run specific market
python scripts/run_scraper.py --market coto

# Run with specific categories
python scripts/run_scraper.py --market carrefour --categories bebidas almacen
```

### Using Docker
```bash
# Execute scraper in container
docker exec supermarket_scraper python -m scrapers.run_scraper --market coto
```

## 📊 Monitoring

### Health Checks
```bash
# Comprehensive health check
python scripts/health_check.py

# Wait for API to be ready
python scripts/health_check.py --wait
```

### Service Management
```bash
# Stop all services
docker-compose down

# Start services
docker-compose up -d

# Restart specific service
docker-compose restart api

# View real-time logs
docker-compose logs -f
```

## 🔧 Configuration

### Environment Variables
Edit `.env` file to customize:
- Scraping intervals and rate limits
- Enable/disable specific markets
- Database settings
- Logging levels

### Key Settings
```bash
# Scraping behavior
RATE_LIMIT_DELAY=2          # Seconds between requests
HEADLESS_BROWSER=true       # Run browser in headless mode
SCRAPING_INTERVAL=3600      # Auto-scraping interval (1 hour)

# Market toggles
COTO_ENABLED=true
CARREFOUR_ENABLED=true
JUMBO_ENABLED=true
DISCO_ENABLED=true
VEA_ENABLED=true
```

## 📈 Next Steps

### 1. Start Scraping Data
```bash
# Begin with a single market to test
python scripts/run_scraper.py --market coto --categories ofertas

# Monitor progress
curl http://localhost:8000/api/v1/health
```

### 2. Explore the API
- Visit http://localhost:8000/docs
- Try the interactive endpoints
- Test search functionality

### 3. Monitor Performance
```bash
# Check scraping sessions
curl http://localhost:8000/api/v1/stats

# View market-specific data
curl http://localhost:8000/api/v1/markets/coto
```

### 4. Scale Up
- Enable all markets in `.env`
- Adjust rate limits based on performance
- Set up automated scheduling

## 🛠️ Troubleshooting

### Common Issues

**API not responding:**
```bash
docker-compose logs api
docker-compose restart api
```

**Scraper errors:**
```bash
docker-compose logs scraper
# Check Chrome/Chromium installation in container
```

**Database connection issues:**
```bash
docker-compose logs mongodb
# Ensure MongoDB is running and accessible
```

### Performance Tuning

**Slow scraping:**
- Increase `RATE_LIMIT_DELAY` to avoid being blocked
- Run scrapers for specific categories only
- Monitor system resources

**Memory usage:**
- Selenium containers use ~500MB each
- Consider running one market at a time
- Adjust Docker memory limits if needed

## 📞 Support

### Documentation
- `docs/TECHNICAL_ANALYSIS.md` - Website analysis
- `docs/API_DOCUMENTATION.md` - Complete API reference
- `docs/DEPLOYMENT.md` - Production deployment guide

### Logs and Debugging
```bash
# Application logs
docker-compose logs

# Database logs
docker-compose logs mongodb

# Specific service logs
docker-compose logs scraper
```

## 🎯 Success Metrics

Your system is working correctly when:
- ✅ Health check returns "healthy" status
- ✅ All Docker services are "Up"
- ✅ API documentation loads at /docs
- ✅ Markets endpoint returns 5 markets
- ✅ Scrapers can run without errors

**Current Status**: All systems operational! 🚀

Ready to start scraping Argentine supermarket data!
