# Argentine Supermarket Scraper

A comprehensive web scraping application for extracting product data from major Argentine supermarket websites. This project implements the technical approach described in [this Medium article](https://medium.com/@rictorres.uyu/building-an-app-to-scrape-supermarkets-data-b9bb6abcf82c) with enhancements for production use.

## 🏪 Supported Supermarkets

- **Coto Digital** (cotodigital3.com.ar) - Angular SPA with Selenium
- **Carrefour** (carrefour.com.ar) - VTEX platform with hybrid scraping
- **Jumbo** (jumbo.com.ar) - VTEX platform
- **Disco** (disco.com.ar) - VTEX platform
- **Vea** (vea.com.ar) - VTEX platform

## ✨ Features

- **Multi-site scraping** with specialized scrapers for each supermarket
- **Respectful scraping** with rate limiting and robots.txt compliance
- **MongoDB storage** with optimized schemas and indexing
- **FastAPI backend** with comprehensive REST API
- **Docker Compose** for containerized deployment
- **Robust error handling** with retry mechanisms and session tracking
- **Configurable scheduling** with Celery and Redis
- **Product data normalization** and search functionality
- **Price comparison** across markets
- **Promotion detection** and categorization

## 🏗️ Architecture

```
├── scrapers/           # Scraper implementations
│   ├── base_scraper.py     # Base scraper with common functionality
│   ├── vtex_scraper.py     # Base for VTEX-based sites
│   ├── coto_scraper.py     # Coto Digital (Angular SPA)
│   ├── carrefour_scraper.py # Carrefour (VTEX hybrid)
│   ├── jumbo_scraper.py    # Jumbo (VTEX)
│   ├── disco_scraper.py    # Disco (VTEX)
│   ├── vea_scraper.py      # Vea (VTEX)
│   └── scraper_manager.py  # Orchestrates all scrapers
├── api/               # FastAPI backend
│   ├── main.py            # FastAPI application
│   ├── routers/           # API route handlers
│   └── dependencies.py    # Dependency injection
├── data/              # Data models and database
│   ├── models.py          # Pydantic models
│   └── database.py        # MongoDB operations
├── scheduler/         # Task scheduling
│   └── main.py           # Celery-based scheduler
├── config/            # Configuration management
├── docker/            # Docker configurations
├── scripts/           # Utility scripts
└── tests/             # Test suite
```

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd argentine-supermarket-scraper
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Access the API**
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

## Configuration

The application uses environment variables for configuration:

- `MONGODB_URL`: MongoDB connection string
- `SCRAPING_INTERVAL`: Interval between scraping runs (in seconds)
- `RATE_LIMIT_DELAY`: Delay between requests (in seconds)
- `USER_AGENT_ROTATION`: Enable user agent rotation
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)

## Data Schema

Each product record contains:

```json
{
  "name": "Product name",
  "price": "Current price",
  "price_before": "Previous price (if on sale)",
  "brand": "Product brand",
  "image_url": "Product image URL",
  "market": "Supermarket name",
  "category": "Product category",
  "date": "Scraping date",
  "url": "Product URL",
  "availability": "In stock status"
}
```

## API Endpoints

- `GET /products` - List all products with filtering
- `GET /products/search` - Search products by keyword
- `GET /products/market/{market}` - Get products from specific market
- `GET /markets` - List all available markets
- `GET /health` - Health check endpoint

## Development

### Prerequisites

- Python 3.9+
- Docker and Docker Compose
- MongoDB (if running locally)

### Local Development

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run MongoDB**
   ```bash
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

3. **Run scrapers**
   ```bash
   python -m scrapers.run_all
   ```

4. **Run API**
   ```bash
   uvicorn api.main:app --reload
   ```

## Legal and Ethical Considerations

This scraper is designed to:
- Respect robots.txt files
- Implement rate limiting to avoid overwhelming servers
- Use appropriate delays between requests
- Rotate user agents to appear more natural
- Handle errors gracefully without retrying excessively

**Important**: Always ensure compliance with the terms of service of the websites you're scraping and applicable laws in your jurisdiction.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is for educational and research purposes. Users are responsible for ensuring compliance with website terms of service and applicable laws.
