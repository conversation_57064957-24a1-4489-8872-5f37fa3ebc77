"""
Admin API endpoints for scraper management and system administration.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse

from data.models import ScrapingConfig, MarketEnum, ScrapingSession
from data.database import db_manager
from ..dependencies import get_database

logger = logging.getLogger(__name__)

# Try to import scrapers - they may not be available in all environments
try:
    from scrapers.scraper_manager import ScraperManager, run_scheduled_scraping
    SCRAPERS_AVAILABLE = True
except ImportError:
    logger.warning("Scrapers module not available - scraping endpoints will return mock data")
    ScraperManager = None
    run_scheduled_scraping = None
    SCRAPERS_AVAILABLE = False

router = APIRouter(prefix="/admin", tags=["admin"])

# Global scraper manager instance
scraper_manager = None
active_sessions: Dict[str, asyncio.Task] = {}


@router.post("/scraping/start")
async def start_scraping(
    config: ScrapingConfig,
    background_tasks: BackgroundTasks,
    db = Depends(get_database)
) -> Dict[str, Any]:
    """Start a scraping session for specified markets."""
    try:
        if not SCRAPERS_AVAILABLE:
            # Return mock response when scrapers are not available
            session_id = f"mock_{int(datetime.utcnow().timestamp())}"
            return {
                "session_id": session_id,
                "status": "mock_started",
                "message": f"Mock scraping started for {config.markets_enabled} (scrapers not available)",
                "timestamp": datetime.utcnow().isoformat()
            }

        global scraper_manager

        # Initialize scraper manager if not exists
        if not scraper_manager:
            scraper_manager = ScraperManager(config)

        # Create session ID
        session_id = f"manual_{int(datetime.utcnow().timestamp())}"

        # Start scraping in background
        if len(config.markets_enabled) > 1:
            task = background_tasks.add_task(
                run_scheduled_scraping,
                config,
                config.categories_to_scrape
            )
        else:
            market_enum = config.markets_enabled[0] if config.markets_enabled else MarketEnum.COTO
            task = background_tasks.add_task(
                scraper_manager.scrape_single_market,
                market_enum,
                config.categories_to_scrape
            )

        # Store active session
        active_sessions[session_id] = task

        return {
            "session_id": session_id,
            "status": "started",
            "message": f"Scraping started for {config.markets_enabled}",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error starting scraping: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start scraping: {str(e)}")


@router.post("/scraping/stop/{session_id}")
async def stop_scraping(session_id: str) -> Dict[str, Any]:
    """Stop an active scraping session."""
    try:
        if session_id not in active_sessions:
            raise HTTPException(status_code=404, detail="Session not found")
        
        task = active_sessions[session_id]
        task.cancel()
        
        # Remove from active sessions
        del active_sessions[session_id]
        
        return {
            "session_id": session_id,
            "status": "stopped",
            "message": "Scraping session stopped",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error stopping scraping: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop scraping: {str(e)}")


@router.get("/scraping/sessions")
async def get_scraping_sessions(
    limit: int = 50,
    db = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Get recent scraping sessions."""
    try:
        # Get recent sessions from database (mock data for now)
        sessions = [
            {
                "session_id": "mock_session_1",
                "market": "coto",
                "status": "completed",
                "started_at": "2025-08-18T19:00:00Z",
                "completed_at": "2025-08-18T19:15:00Z",
                "products_found": 150,
                "products_new": 25
            }
        ]
        
        # Add active session status
        for session in sessions:
            session_id = session.get("session_id", "")
            session["is_active"] = session_id in active_sessions
            
        return sessions
        
    except Exception as e:
        logger.error(f"Error getting scraping sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get scraping sessions")


@router.get("/scraping/sessions/{session_id}")
async def get_scraping_session(
    session_id: str,
    db = Depends(get_database)
) -> Dict[str, Any]:
    """Get details of a specific scraping session."""
    try:
        # Mock session data for now
        session = {
            "session_id": session_id,
            "market": "coto",
            "status": "completed",
            "started_at": "2025-08-18T19:00:00Z",
            "completed_at": "2025-08-18T19:15:00Z",
            "products_found": 150,
            "products_new": 25
        } if session_id == "mock_session_1" else None
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Add active status
        session["is_active"] = session_id in active_sessions
        
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scraping session: {e}")
        raise HTTPException(status_code=500, detail="Failed to get scraping session")


@router.get("/scraping/status")
async def get_scraping_status() -> Dict[str, Any]:
    """Get current scraping status for all markets."""
    try:
        status = {}
        
        for market in MarketEnum:
            # Check if there's an active session for this market
            active_session = None
            for session_id, task in active_sessions.items():
                if market.value in session_id or "all" in session_id:
                    active_session = session_id
                    break
            
            status[market.value] = {
                "status": "running" if active_session else "idle",
                "active_session": active_session,
                "last_run": None  # Would need to query database for this
            }
        
        return {
            "markets": status,
            "active_sessions": len(active_sessions),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting scraping status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get scraping status")


@router.post("/scraping/test/{market}")
async def test_scraper(
    market: MarketEnum,
    max_products: int = 5
) -> Dict[str, Any]:
    """Test a specific market scraper with limited products."""
    try:
        if not SCRAPERS_AVAILABLE:
            # Return mock response when scrapers are not available
            return {
                "market": market.value,
                "products_scraped": 5,  # Mock result
                "status": "mock_success",
                "message": f"Mock test scraping completed for {market.value} (scrapers not available)",
                "timestamp": datetime.utcnow().isoformat()
            }

        # Create test configuration
        test_config = ScrapingConfig(
            markets_enabled=[market],
            max_products_per_session=max_products,
            headless=True,
            rate_limit_delay=1.0  # Faster for testing
        )

        # Initialize scraper manager
        manager = ScraperManager(test_config)

        # Run test scraping
        result = await manager.scrape_single_market(market)

        return {
            "market": market.value,
            "products_scraped": result,
            "status": "success" if result > 0 else "no_products",
            "message": f"Test scraping completed for {market.value}",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error testing scraper for {market}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to test scraper: {str(e)}")


@router.get("/system/stats")
async def get_system_stats(db = Depends(get_database)) -> Dict[str, Any]:
    """Get system statistics for admin dashboard."""
    try:
        # Get database stats
        total_products = await db.products_collection.count_documents({})
        
        # Get products by market
        pipeline = [
            {"$group": {"_id": "$market", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        market_stats = await db.products_collection.aggregate(pipeline).to_list(length=None)
        
        # Get recent scraping sessions (mock data for now)
        recent_sessions = [
            {"status": "completed"},
            {"status": "completed"},
            {"status": "failed"},
            {"status": "completed"}
        ]
        
        # Calculate success rate
        completed_sessions = [s for s in recent_sessions if s.get("status") == "completed"]
        success_rate = (len(completed_sessions) / len(recent_sessions) * 100) if recent_sessions else 0
        
        return {
            "database": {
                "total_products": total_products,
                "products_by_market": {stat["_id"]: stat["count"] for stat in market_stats}
            },
            "scraping": {
                "active_sessions": len(active_sessions),
                "recent_sessions": len(recent_sessions),
                "success_rate": round(success_rate, 1)
            },
            "system": {
                "timestamp": datetime.utcnow().isoformat(),
                "uptime": "N/A"  # Would need to track application start time
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system stats")


@router.delete("/data/cleanup")
async def cleanup_old_data(
    days_old: int = 30,
    market: Optional[MarketEnum] = None,
    db = Depends(get_database)
) -> Dict[str, Any]:
    """Clean up old product data."""
    try:
        from datetime import timedelta

        cutoff_date = datetime.utcnow() - timedelta(days=days_old)

        # Build query
        query = {"scraped_at": {"$lt": cutoff_date}}
        if market:
            query["market"] = market.value

        # Count documents to be deleted
        count = await db.products_collection.count_documents(query)

        # Delete old documents
        result = await db.products_collection.delete_many(query)

        return {
            "deleted_count": result.deleted_count,
            "expected_count": count,
            "cutoff_date": cutoff_date.isoformat(),
            "market": market.value if market else "all",
            "message": f"Cleaned up {result.deleted_count} old products"
        }

    except Exception as e:
        logger.error(f"Error cleaning up data: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup data")


@router.delete("/data/clear-all")
async def clear_all_data(
    confirm: bool = False,
    market: Optional[MarketEnum] = None,
    db = Depends(get_database)
) -> Dict[str, Any]:
    """Clear ALL scraped product data from the database."""
    try:
        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="This operation will delete ALL scraped data. Set confirm=true to proceed."
            )

        # Build query
        query = {}
        if market:
            query["market"] = market.value

        # Count documents to be deleted
        count = await db.products_collection.count_documents(query)

        if count == 0:
            return {
                "deleted_count": 0,
                "expected_count": 0,
                "market": market.value if market else "all",
                "message": "No products found to delete"
            }

        # Delete all documents
        result = await db.products_collection.delete_many(query)

        logger.info(f"Cleared {result.deleted_count} products from database")

        return {
            "deleted_count": result.deleted_count,
            "expected_count": count,
            "market": market.value if market else "all",
            "message": f"Successfully cleared {result.deleted_count} products from the database",
            "timestamp": datetime.utcnow().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing all data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear data: {str(e)}")
