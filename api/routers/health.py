"""
Health check API router.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends

from data.database import DatabaseManager
from ..dependencies import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check(
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Any]:
    """Basic health check endpoint."""
    try:
        # Test database connection
        await db.client.admin.command('ping')
        
        # Get basic stats
        total_products = await db.products_collection.count_documents({})
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": "connected",
            "total_products": total_products
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": "disconnected",
            "error": str(e)
        }


@router.get("/detailed")
async def detailed_health_check(
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Any]:
    """Detailed health check with more information."""
    try:
        # Test database connection
        await db.client.admin.command('ping')
        
        # Get detailed stats
        stats = await db.get_product_stats()
        
        # Check recent activity
        recent_products = await db.products_collection.find(
            {},
            sort=[("last_updated", -1)],
            limit=1
        ).to_list(1)
        
        last_update = recent_products[0]["last_updated"] if recent_products else None
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": {
                "status": "connected",
                "total_products": stats.total_products,
                "products_by_market": stats.products_by_market,
                "last_update": last_update.isoformat() if last_update else None
            },
            "api": {
                "version": "1.0.0",
                "endpoints_available": True
            }
        }
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")
