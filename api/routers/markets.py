"""
Markets API router.
"""

import logging
from typing import List, Dict, Any

from fastapi import APIRouter, HTTPException, Depends

from data.models import MarketEnum
from data.database import DatabaseManager
from ..dependencies import get_database

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def list_markets(
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """List all available markets with statistics."""
    try:
        stats = await db.get_product_stats()
        
        markets = []
        for market in MarketEnum:
            market_info = {
                "name": market.value,
                "display_name": market.value.title(),
                "product_count": stats.products_by_market.get(market.value, 0),
                "average_price": stats.average_price_by_market.get(market.value, 0)
            }
            markets.append(market_info)
        
        return markets
        
    except Exception as e:
        logger.error(f"Error listing markets: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{market}")
async def get_market_info(
    market: MarketEnum,
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Any]:
    """Get detailed information about a specific market."""
    try:
        # Get market statistics
        stats = await db.get_product_stats()
        
        # Get categories for this market
        pipeline = [
            {"$match": {"market": market.value, "category": {"$ne": None}}},
            {"$group": {"_id": "$category", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        category_results = await db.products_collection.aggregate(pipeline).to_list(None)
        categories = [
            {"name": result["_id"], "product_count": result["count"]}
            for result in category_results
        ]
        
        # Get recent products
        recent_products = await db.search_products({
            "market": market.value,
            "limit": 10,
            "sort_by": "scraped_at",
            "sort_order": -1
        })
        
        market_info = {
            "name": market.value,
            "display_name": market.value.title(),
            "product_count": stats.products_by_market.get(market.value, 0),
            "average_price": stats.average_price_by_market.get(market.value, 0),
            "categories": categories,
            "recent_products": recent_products[:5]  # Limit to 5 for summary
        }
        
        return market_info
        
    except Exception as e:
        logger.error(f"Error getting market info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{market}/categories")
async def get_market_categories(
    market: MarketEnum,
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Get categories available in a specific market."""
    try:
        pipeline = [
            {"$match": {"market": market.value, "category": {"$ne": None}}},
            {"$group": {"_id": "$category", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        
        results = await db.products_collection.aggregate(pipeline).to_list(None)
        categories = [
            {"name": result["_id"], "product_count": result["count"]}
            for result in results
        ]
        
        return categories
        
    except Exception as e:
        logger.error(f"Error getting market categories: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{market}/stats")
async def get_market_stats(
    market: MarketEnum,
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Any]:
    """Get detailed statistics for a specific market."""
    try:
        # Product count
        total_products = await db.products_collection.count_documents({"market": market.value})
        
        # Average price
        price_pipeline = [
            {"$match": {"market": market.value}},
            {"$group": {"_id": None, "avg_price": {"$avg": "$price.current"}}}
        ]
        price_result = await db.products_collection.aggregate(price_pipeline).to_list(1)
        avg_price = price_result[0]["avg_price"] if price_result else 0
        
        # Products with promotions
        promotions_count = await db.products_collection.count_documents({
            "market": market.value,
            "promotions": {"$ne": []}
        })
        
        # Out of stock products
        out_of_stock = await db.products_collection.count_documents({
            "market": market.value,
            "availability": "out_of_stock"
        })
        
        # Price range
        price_range_pipeline = [
            {"$match": {"market": market.value}},
            {"$group": {
                "_id": None,
                "min_price": {"$min": "$price.current"},
                "max_price": {"$max": "$price.current"}
            }}
        ]
        price_range_result = await db.products_collection.aggregate(price_range_pipeline).to_list(1)
        price_range = price_range_result[0] if price_range_result else {"min_price": 0, "max_price": 0}
        
        # Last update
        last_product = await db.products_collection.find_one(
            {"market": market.value},
            sort=[("last_updated", -1)]
        )
        last_update = last_product["last_updated"] if last_product else None
        
        stats = {
            "market": market.value,
            "total_products": total_products,
            "average_price": round(avg_price, 2) if avg_price else 0,
            "products_with_promotions": promotions_count,
            "out_of_stock_products": out_of_stock,
            "price_range": {
                "min": price_range.get("min_price", 0),
                "max": price_range.get("max_price", 0)
            },
            "last_update": last_update
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting market stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
