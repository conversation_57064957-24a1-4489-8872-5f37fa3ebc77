"""
Products API router.
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from bson import ObjectId

from data.models import SearchQuery, MarketEnum, ProductAvailability
from data.database import DatabaseManager
from ..dependencies import get_database

def convert_objectid_to_str(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Convert ObjectId fields to strings for JSON serialization."""
    if doc is None:
        return doc

    # Convert _id field
    if "_id" in doc and isinstance(doc["_id"], ObjectId):
        doc["_id"] = str(doc["_id"])

    # Recursively convert nested documents
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            doc[key] = str(value)
        elif isinstance(value, dict):
            doc[key] = convert_objectid_to_str(value)
        elif isinstance(value, list):
            doc[key] = [convert_objectid_to_str(item) if isinstance(item, dict) else str(item) if isinstance(item, ObjectId) else item for item in value]

    return doc

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def list_products(
    market: Optional[MarketEnum] = Query(None, description="Filter by market"),
    category: Optional[str] = Query(None, description="Filter by category"),
    min_price: Optional[float] = Query(None, description="Minimum price filter"),
    max_price: Optional[float] = Query(None, description="Maximum price filter"),
    availability: Optional[ProductAvailability] = Query(None, description="Filter by availability"),
    has_promotion: Optional[bool] = Query(None, description="Filter products with promotions"),
    skip: int = Query(0, ge=0, description="Number of products to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Number of products to return"),
    sort_by: str = Query("scraped_at", description="Field to sort by"),
    sort_order: int = Query(-1, description="Sort order (-1 for desc, 1 for asc)"),
    db: DatabaseManager = Depends(get_database)
) -> Dict[str, Any]:
    """List products with optional filtering and pagination."""
    try:
        search_query = SearchQuery(
            market=market,
            category=category,
            min_price=min_price,
            max_price=max_price,
            availability=availability,
            has_promotion=has_promotion,
            skip=skip,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        products = await db.search_products(search_query)
        # Convert ObjectIds to strings for JSON serialization
        products_converted = [convert_objectid_to_str(product) for product in products]

        # Get total count for pagination
        total_count = await db.products_collection.count_documents({
            **({"market": market.value} if market else {}),
            **({"category": {"$regex": category, "$options": "i"}} if category else {}),
            **({"price.current": {"$gte": min_price}} if min_price is not None else {}),
            **({"price.current": {"$lte": max_price}} if max_price is not None else {}),
            **({"availability": availability.value} if availability else {}),
            **({"promotions": {"$ne": []}} if has_promotion else {})
        })

        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        current_page = (skip // limit) + 1

        return {
            "products": products_converted,
            "total": total_count,
            "page": current_page,
            "limit": limit,
            "total_pages": total_pages,
            "filters_applied": {
                "market": [market.value] if market else None,
                "category": [category] if category else None,
                "min_price": min_price,
                "max_price": max_price,
                "availability": availability.value if availability else None,
                "on_promotion": has_promotion
            }
        }
        
    except Exception as e:
        logger.error(f"Error listing products: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search")
async def search_products(
    q: str = Query(..., description="Search query"),
    market: Optional[MarketEnum] = Query(None, description="Filter by market"),
    category: Optional[str] = Query(None, description="Filter by category"),
    min_price: Optional[float] = Query(None, description="Minimum price filter"),
    max_price: Optional[float] = Query(None, description="Maximum price filter"),
    availability: Optional[ProductAvailability] = Query(None, description="Filter by availability"),
    has_promotion: Optional[bool] = Query(None, description="Filter products with promotions"),
    skip: int = Query(0, ge=0, description="Number of products to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Number of products to return"),
    sort_by: str = Query("scraped_at", description="Field to sort by"),
    sort_order: int = Query(-1, description="Sort order (-1 for desc, 1 for asc)"),
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Search products by keyword with optional filtering."""
    try:
        search_query = SearchQuery(
            query=q,
            market=market,
            category=category,
            min_price=min_price,
            max_price=max_price,
            availability=availability,
            has_promotion=has_promotion,
            skip=skip,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        products = await db.search_products(search_query)
        return products
        
    except Exception as e:
        logger.error(f"Error searching products: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/market/{market}")
async def get_products_by_market(
    market: MarketEnum,
    category: Optional[str] = Query(None, description="Filter by category"),
    min_price: Optional[float] = Query(None, description="Minimum price filter"),
    max_price: Optional[float] = Query(None, description="Maximum price filter"),
    availability: Optional[ProductAvailability] = Query(None, description="Filter by availability"),
    has_promotion: Optional[bool] = Query(None, description="Filter products with promotions"),
    skip: int = Query(0, ge=0, description="Number of products to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Number of products to return"),
    sort_by: str = Query("scraped_at", description="Field to sort by"),
    sort_order: int = Query(-1, description="Sort order (-1 for desc, 1 for asc)"),
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Get products from a specific market."""
    try:
        search_query = SearchQuery(
            market=market,
            category=category,
            min_price=min_price,
            max_price=max_price,
            availability=availability,
            has_promotion=has_promotion,
            skip=skip,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        products = await db.search_products(search_query)
        return products
        
    except Exception as e:
        logger.error(f"Error getting products by market: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/categories")
async def get_categories(
    market: Optional[MarketEnum] = Query(None, description="Filter by market"),
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Get list of available categories with product counts."""
    try:
        # Build aggregation pipeline
        pipeline = []

        if market:
            pipeline.append({"$match": {"market": market.value}})

        pipeline.extend([
            {"$match": {"category": {"$ne": None}}},
            {"$group": {"_id": "$category", "product_count": {"$sum": 1}}},
            {"$sort": {"_id": 1}}
        ])

        results = await db.products_collection.aggregate(pipeline).to_list(None)
        categories = [
            {
                "name": result["_id"],
                "display_name": result["_id"],
                "product_count": result["product_count"]
            }
            for result in results if result["_id"]
        ]

        return categories
        
    except Exception as e:
        logger.error(f"Error getting categories: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/brands")
async def get_brands(
    market: Optional[MarketEnum] = Query(None, description="Filter by market"),
    category: Optional[str] = Query(None, description="Filter by category"),
    db: DatabaseManager = Depends(get_database)
) -> List[str]:
    """Get list of available brands."""
    try:
        # Build aggregation pipeline
        pipeline = []
        
        match_conditions = {"brand": {"$ne": None}}
        if market:
            match_conditions["market"] = market.value
        if category:
            match_conditions["category"] = category
        
        pipeline.extend([
            {"$match": match_conditions},
            {"$group": {"_id": "$brand"}},
            {"$sort": {"_id": 1}}
        ])
        
        results = await db.products_collection.aggregate(pipeline).to_list(None)
        brands = [result["_id"] for result in results if result["_id"]]
        
        return brands
        
    except Exception as e:
        logger.error(f"Error getting brands: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/promotions")
async def get_products_with_promotions(
    market: Optional[MarketEnum] = Query(None, description="Filter by market"),
    category: Optional[str] = Query(None, description="Filter by category"),
    skip: int = Query(0, ge=0, description="Number of products to skip"),
    limit: int = Query(50, ge=1, le=1000, description="Number of products to return"),
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Get products that have promotions."""
    try:
        search_query = SearchQuery(
            market=market,
            category=category,
            has_promotion=True,
            skip=skip,
            limit=limit,
            sort_by="scraped_at",
            sort_order=-1
        )
        
        products = await db.search_products(search_query)
        return products
        
    except Exception as e:
        logger.error(f"Error getting products with promotions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/price-comparison")
async def compare_prices(
    product_name: str = Query(..., description="Product name to compare"),
    db: DatabaseManager = Depends(get_database)
) -> List[Dict[str, Any]]:
    """Compare prices of a product across different markets."""
    try:
        # Search for similar products across all markets
        search_query = SearchQuery(
            query=product_name,
            limit=100,
            sort_by="price.current",
            sort_order=1  # Ascending to show cheapest first
        )
        
        products = await db.search_products(search_query)
        
        # Group by market and find best prices
        market_prices = {}
        for product in products:
            market = product.get("market")
            price = product.get("price", {}).get("current")
            
            if market and price:
                if market not in market_prices or price < market_prices[market]["price"]:
                    market_prices[market] = {
                        "market": market,
                        "price": price,
                        "product_name": product.get("name"),
                        "url": product.get("url"),
                        "availability": product.get("availability")
                    }
        
        # Convert to list and sort by price
        comparison = list(market_prices.values())
        comparison.sort(key=lambda x: x["price"])
        
        return comparison
        
    except Exception as e:
        logger.error(f"Error comparing prices: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
