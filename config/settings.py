"""
Application settings and configuration.
"""

import os
from typing import List
from pydantic import BaseSettings, Field
from data.models import MarketEnum


class Settings(BaseSettings):
    """Application settings."""
    
    # Database settings
    mongodb_url: str = Field(
        default="mongodb://localhost:27017/supermarket_data",
        env="MONGODB_URL"
    )
    
    # API settings
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    
    # Scraping settings
    scraping_interval: int = Field(default=3600, env="SCRAPING_INTERVAL")  # 1 hour
    rate_limit_delay: float = Field(default=2.0, env="RATE_LIMIT_DELAY")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    timeout: int = Field(default=30, env="TIMEOUT")
    headless_browser: bool = Field(default=True, env="HEADLESS_BROWSER")
    user_agent_rotation: bool = Field(default=True, env="USER_AGENT_ROTATION")
    
    # Market settings
    coto_enabled: bool = Field(default=True, env="COTO_ENABLED")
    carrefour_enabled: bool = Field(default=True, env="CARREFOUR_ENABLED")
    jumbo_enabled: bool = Field(default=True, env="JUMBO_ENABLED")
    disco_enabled: bool = Field(default=True, env="DISCO_ENABLED")
    vea_enabled: bool = Field(default=True, env="VEA_ENABLED")
    
    # Scheduler settings
    scraping_schedule: str = Field(default="0 */6 * * *", env="SCRAPING_SCHEDULE")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Data retention
    data_retention_days: int = Field(default=30, env="DATA_RETENTION_DAYS")
    cleanup_schedule: str = Field(default="0 2 * * *", env="CLEANUP_SCHEDULE")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default=None, env="LOG_FILE")
    
    # Proxy settings (optional)
    use_proxy: bool = Field(default=False, env="USE_PROXY")
    proxy_url: str = Field(default=None, env="PROXY_URL")
    proxy_username: str = Field(default=None, env="PROXY_USERNAME")
    proxy_password: str = Field(default=None, env="PROXY_PASSWORD")
    
    # Notification settings (optional)
    enable_notifications: bool = Field(default=False, env="ENABLE_NOTIFICATIONS")
    slack_webhook_url: str = Field(default=None, env="SLACK_WEBHOOK_URL")
    email_smtp_server: str = Field(default=None, env="EMAIL_SMTP_SERVER")
    email_username: str = Field(default=None, env="EMAIL_USERNAME")
    email_password: str = Field(default=None, env="EMAIL_PASSWORD")
    email_recipients: str = Field(default=None, env="EMAIL_RECIPIENTS")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        case_sensitive = False
    
    @property
    def enabled_markets(self) -> List[MarketEnum]:
        """Get list of enabled markets."""
        markets = []
        if self.coto_enabled:
            markets.append(MarketEnum.COTO)
        if self.carrefour_enabled:
            markets.append(MarketEnum.CARREFOUR)
        if self.jumbo_enabled:
            markets.append(MarketEnum.JUMBO)
        if self.disco_enabled:
            markets.append(MarketEnum.DISCO)
        if self.vea_enabled:
            markets.append(MarketEnum.VEA)
        return markets
    
    @property
    def email_recipients_list(self) -> List[str]:
        """Get email recipients as a list."""
        if self.email_recipients:
            return [email.strip() for email in self.email_recipients.split(",")]
        return []


# Global settings instance
settings = Settings()
