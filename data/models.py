"""
Data models for the supermarket scraper application.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class MarketEnum(str, Enum):
    """Supported supermarket chains."""
    COTO = "coto"
    CARREFOUR = "carrefour"
    JUMBO = "jumbo"
    DISCO = "disco"
    VEA = "vea"


class ProductAvailability(str, Enum):
    """Product availability status."""
    IN_STOCK = "in_stock"
    OUT_OF_STOCK = "out_of_stock"
    LIMITED_STOCK = "limited_stock"
    UNKNOWN = "unknown"


class PriceInfo(BaseModel):
    """Price information for a product."""
    current: float = Field(..., description="Current price")
    previous: Optional[float] = Field(None, description="Previous price if on sale")
    currency: str = Field(default="ARS", description="Currency code")
    discount_percentage: Optional[float] = Field(None, description="Discount percentage if on sale")
    
    @validator('current', 'previous')
    def validate_price(cls, v):
        if v is not None and v < 0:
            raise ValueError('Price cannot be negative')
        return v


class ProductPromotion(BaseModel):
    """Product promotion information."""
    type: str = Field(..., description="Promotion type (e.g., '2x1', '30% off')")
    description: Optional[str] = Field(None, description="Promotion description")
    valid_until: Optional[datetime] = Field(None, description="Promotion expiry date")


class Product(BaseModel):
    """Product model representing a scraped product."""
    
    # Basic product information
    name: str = Field(..., description="Product name")
    brand: Optional[str] = Field(None, description="Product brand")
    category: Optional[str] = Field(None, description="Product category")
    subcategory: Optional[str] = Field(None, description="Product subcategory")
    
    # Price information
    price: PriceInfo = Field(..., description="Price information")
    
    # Market information
    market: MarketEnum = Field(..., description="Supermarket chain")
    market_product_id: Optional[str] = Field(None, description="Product ID in the market's system")
    
    # URLs and images
    url: str = Field(..., description="Product URL")
    image_url: Optional[str] = Field(None, description="Product image URL")
    
    # Availability and stock
    availability: ProductAvailability = Field(default=ProductAvailability.UNKNOWN)
    stock_quantity: Optional[int] = Field(None, description="Available stock quantity")
    
    # Promotions
    promotions: List[ProductPromotion] = Field(default_factory=list)
    
    # Product details
    description: Optional[str] = Field(None, description="Product description")
    specifications: Dict[str, Any] = Field(default_factory=dict, description="Product specifications")
    weight: Optional[str] = Field(None, description="Product weight/size")
    unit: Optional[str] = Field(None, description="Unit of measurement")
    
    # Metadata
    scraped_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    scraper_version: str = Field(default="1.0.0")
    
    # Search and indexing
    search_keywords: List[str] = Field(default_factory=list)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ScrapingSession(BaseModel):
    """Model for tracking scraping sessions."""
    
    session_id: str = Field(..., description="Unique session identifier")
    market: MarketEnum = Field(..., description="Market being scraped")
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = Field(None)
    status: str = Field(default="running")  # running, completed, failed
    
    # Statistics
    products_found: int = Field(default=0)
    products_updated: int = Field(default=0)
    products_new: int = Field(default=0)
    errors_count: int = Field(default=0)
    
    # Configuration used
    scraper_config: Dict[str, Any] = Field(default_factory=dict)
    
    # Error tracking
    errors: List[Dict[str, Any]] = Field(default_factory=list)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class SearchQuery(BaseModel):
    """Model for product search queries."""
    
    query: Optional[str] = Field(None, description="Search query string")
    market: Optional[MarketEnum] = Field(None, description="Filter by market")
    category: Optional[str] = Field(None, description="Filter by category")
    min_price: Optional[float] = Field(None, description="Minimum price filter")
    max_price: Optional[float] = Field(None, description="Maximum price filter")
    availability: Optional[ProductAvailability] = Field(None, description="Filter by availability")
    has_promotion: Optional[bool] = Field(None, description="Filter products with promotions")
    
    # Pagination
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=50, ge=1, le=1000)
    
    # Sorting
    sort_by: str = Field(default="scraped_at")
    sort_order: int = Field(default=-1)  # -1 for descending, 1 for ascending


class ProductStats(BaseModel):
    """Statistics about products in the database."""
    
    total_products: int
    products_by_market: Dict[str, int]
    products_by_category: Dict[str, int]
    average_price_by_market: Dict[str, float]
    last_update: datetime
    products_with_promotions: int
    out_of_stock_products: int


class ScrapingConfig(BaseModel):
    """Configuration for scraping operations."""
    
    # General settings
    rate_limit_delay: float = Field(default=2.0, description="Delay between requests in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    
    # Browser settings
    headless: bool = Field(default=True, description="Run browser in headless mode")
    user_agent_rotation: bool = Field(default=True, description="Rotate user agents")
    
    # Market-specific settings
    markets_enabled: List[MarketEnum] = Field(default_factory=lambda: list(MarketEnum))
    
    # Data settings
    max_products_per_session: Optional[int] = Field(None, description="Maximum products to scrape per session")
    categories_to_scrape: List[str] = Field(default_factory=list, description="Specific categories to scrape")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
