// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the supermarket_data database
db = db.getSiblingDB('supermarket_data');

// Create collections with validation
db.createCollection('products', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['name', 'price', 'market', 'url'],
            properties: {
                name: {
                    bsonType: 'string',
                    description: 'Product name is required and must be a string'
                },
                price: {
                    bsonType: 'object',
                    required: ['current'],
                    properties: {
                        current: {
                            bsonType: 'number',
                            minimum: 0,
                            description: 'Current price must be a positive number'
                        },
                        previous: {
                            bsonType: ['number', 'null'],
                            minimum: 0
                        },
                        currency: {
                            bsonType: 'string',
                            enum: ['ARS', 'USD', 'EUR']
                        }
                    }
                },
                market: {
                    bsonType: 'string',
                    enum: ['coto', 'carrefour', 'jumbo', 'disco', 'vea'],
                    description: 'Market must be one of the supported supermarkets'
                },
                url: {
                    bsonType: 'string',
                    description: 'Product URL is required'
                },
                availability: {
                    bsonType: 'string',
                    enum: ['in_stock', 'out_of_stock', 'limited_stock', 'unknown']
                }
            }
        }
    }
});

db.createCollection('scraping_sessions', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['session_id', 'market', 'started_at'],
            properties: {
                session_id: {
                    bsonType: 'string',
                    description: 'Session ID is required'
                },
                market: {
                    bsonType: 'string',
                    enum: ['coto', 'carrefour', 'jumbo', 'disco', 'vea']
                },
                status: {
                    bsonType: 'string',
                    enum: ['running', 'completed', 'failed', 'no_products_found']
                }
            }
        }
    }
});

// Create indexes for better performance
db.products.createIndex({ 'market': 1, 'market_product_id': 1 }, { unique: true });
db.products.createIndex({ 'name': 'text', 'brand': 'text', 'description': 'text' });
db.products.createIndex({ 'market': 1 });
db.products.createIndex({ 'category': 1 });
db.products.createIndex({ 'price.current': 1 });
db.products.createIndex({ 'availability': 1 });
db.products.createIndex({ 'scraped_at': -1 });
db.products.createIndex({ 'last_updated': -1 });

db.scraping_sessions.createIndex({ 'session_id': 1 }, { unique: true });
db.scraping_sessions.createIndex({ 'market': 1 });
db.scraping_sessions.createIndex({ 'started_at': -1 });
db.scraping_sessions.createIndex({ 'status': 1 });

print('Database initialization completed successfully');
