FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for Chrome and Selenium
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    xvfb \
    gcc \
    g++ \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome and ChromeDriver based on architecture
RUN ARCH=$(dpkg --print-architecture) && \
    if [ "$ARCH" = "amd64" ]; then \
        # Install Chrome
        wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor -o /usr/share/keyrings/googlechrome-linux-keyring.gpg && \
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/googlechrome-linux-keyring.gpg] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google.list && \
        apt-get update && \
        apt-get install -y google-chrome-stable && \
        # Install ChromeDriver using Chrome for Testing API (newer approach)
        CHROME_VERSION=$(google-chrome --version | awk '{print $3}') && \
        echo "Chrome version: $CHROME_VERSION" && \
        # Try Chrome for Testing API first (for newer versions)
        CHROMEDRIVER_URL=$(curl -s "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json" | \
            python3 -c "import sys, json; data=json.load(sys.stdin); versions=[v for v in data['versions'] if v['version'].startswith('$CHROME_VERSION'.split('.')[0])]; print(versions[-1]['downloads']['chromedriver'][0]['url'] if versions and 'chromedriver' in versions[-1]['downloads'] else '')" 2>/dev/null) && \
        if [ -n "$CHROMEDRIVER_URL" ]; then \
            echo "Using Chrome for Testing API: $CHROMEDRIVER_URL" && \
            wget -O /tmp/chromedriver.zip "$CHROMEDRIVER_URL" && \
            unzip /tmp/chromedriver.zip -d /tmp/ && \
            find /tmp -name "chromedriver" -type f -exec mv {} /usr/local/bin/chromedriver \; && \
            chmod +x /usr/local/bin/chromedriver; \
        else \
            echo "Chrome for Testing API failed, trying legacy method" && \
            CHROME_MAJOR=$(echo $CHROME_VERSION | cut -d. -f1) && \
            CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_MAJOR" 2>/dev/null || echo "114.0.5735.90") && \
            echo "Using ChromeDriver version: $CHROMEDRIVER_VERSION" && \
            wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/$CHROMEDRIVER_VERSION/chromedriver_linux64.zip" && \
            unzip /tmp/chromedriver.zip -d /tmp/ && \
            mv /tmp/chromedriver /usr/local/bin/chromedriver && \
            chmod +x /usr/local/bin/chromedriver; \
        fi && \
        rm -f /tmp/chromedriver.zip && \
        rm -rf /tmp/chromedriver-* && \
        rm -rf /var/lib/apt/lists/*; \
    else \
        echo "Installing Chromium for non-amd64 architecture" && \
        apt-get update && \
        apt-get install -y chromium chromium-driver && \
        rm -rf /var/lib/apt/lists/* && \
        ln -sf /usr/bin/chromium /usr/bin/google-chrome && \
        ln -sf /usr/bin/chromedriver /usr/local/bin/chromedriver; \
    fi

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY scrapers/ ./scrapers/
COPY data/ ./data/
COPY config/ ./config/

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Set display for headless Chrome
ENV DISPLAY=:99

# Run the scraper
CMD ["python", "-m", "scrapers.scraper_manager"]
