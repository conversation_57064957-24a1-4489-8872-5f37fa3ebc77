# API Documentation

The Argentine Supermarket Scraper provides a comprehensive REST API for accessing scraped product data.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Currently, the API does not require authentication. For production use, consider implementing API keys or OAuth.

## Endpoints

### Health Check

#### GET /api/v1/health
Basic health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "database": "connected",
  "total_products": 1234
}
```

#### GET /api/v1/health/detailed
Detailed health check with comprehensive system information.

### Products

#### GET /api/v1/products
List products with optional filtering and pagination.

**Query Parameters:**
- `market` (optional): Filter by market (coto, carrefour, jumbo, disco, vea)
- `category` (optional): Filter by category
- `min_price` (optional): Minimum price filter
- `max_price` (optional): Maximum price filter
- `availability` (optional): Filter by availability (in_stock, out_of_stock, limited_stock, unknown)
- `has_promotion` (optional): Filter products with promotions (true/false)
- `skip` (optional): Number of products to skip (default: 0)
- `limit` (optional): Number of products to return (default: 50, max: 1000)
- `sort_by` (optional): Field to sort by (default: scraped_at)
- `sort_order` (optional): Sort order (-1 for desc, 1 for asc)

**Example:**
```bash
curl "http://localhost:8000/api/v1/products?market=coto&category=Bebidas&limit=10"
```

#### GET /api/v1/products/search
Search products by keyword.

**Query Parameters:**
- `q` (required): Search query
- All other parameters from `/products` endpoint

**Example:**
```bash
curl "http://localhost:8000/api/v1/products/search?q=coca%20cola&market=carrefour"
```

#### GET /api/v1/products/market/{market}
Get products from a specific market.

**Path Parameters:**
- `market`: Market name (coto, carrefour, jumbo, disco, vea)

**Example:**
```bash
curl "http://localhost:8000/api/v1/products/market/jumbo?limit=20"
```

#### GET /api/v1/products/categories
Get list of available categories.

**Query Parameters:**
- `market` (optional): Filter categories by market

**Response:**
```json
[
  "Almacén",
  "Bebidas",
  "Frescos",
  "Congelados"
]
```

#### GET /api/v1/products/brands
Get list of available brands.

**Query Parameters:**
- `market` (optional): Filter brands by market
- `category` (optional): Filter brands by category

#### GET /api/v1/products/promotions
Get products that have promotions.

**Query Parameters:**
- `market` (optional): Filter by market
- `category` (optional): Filter by category
- `skip`, `limit`: Pagination parameters

#### GET /api/v1/products/price-comparison
Compare prices of a product across different markets.

**Query Parameters:**
- `product_name` (required): Product name to compare

**Response:**
```json
[
  {
    "market": "disco",
    "price": 1234.56,
    "product_name": "Coca Cola 2.25L",
    "url": "https://...",
    "availability": "in_stock"
  },
  {
    "market": "jumbo",
    "price": 1299.99,
    "product_name": "Coca Cola 2.25L",
    "url": "https://...",
    "availability": "in_stock"
  }
]
```

### Markets

#### GET /api/v1/markets
List all available markets with statistics.

**Response:**
```json
[
  {
    "name": "coto",
    "display_name": "Coto",
    "product_count": 1234,
    "average_price": 567.89
  }
]
```

#### GET /api/v1/markets/{market}
Get detailed information about a specific market.

**Response:**
```json
{
  "name": "coto",
  "display_name": "Coto",
  "product_count": 1234,
  "average_price": 567.89,
  "categories": [
    {
      "name": "Bebidas",
      "product_count": 123
    }
  ],
  "recent_products": [...]
}
```

#### GET /api/v1/markets/{market}/categories
Get categories available in a specific market.

#### GET /api/v1/markets/{market}/stats
Get detailed statistics for a specific market.

**Response:**
```json
{
  "market": "coto",
  "total_products": 1234,
  "average_price": 567.89,
  "products_with_promotions": 123,
  "out_of_stock_products": 45,
  "price_range": {
    "min": 10.50,
    "max": 15000.00
  },
  "last_update": "2024-01-01T00:00:00Z"
}
```

### Statistics

#### GET /api/v1/stats
Get overall statistics about the scraped data.

**Response:**
```json
{
  "total_products": 5678,
  "products_by_market": {
    "coto": 1234,
    "carrefour": 1123,
    "jumbo": 1100,
    "disco": 1111,
    "vea": 1110
  },
  "products_by_category": {
    "Bebidas": 567,
    "Almacén": 890
  },
  "average_price_by_market": {
    "coto": 567.89,
    "carrefour": 543.21
  },
  "last_update": "2024-01-01T00:00:00Z",
  "products_with_promotions": 234,
  "out_of_stock_products": 123
}
```

## Response Format

### Product Object
```json
{
  "_id": "507f1f77bcf86cd799439011",
  "name": "Coca Cola 2.25L",
  "brand": "Coca Cola",
  "category": "Bebidas",
  "price": {
    "current": 1234.56,
    "previous": 1400.00,
    "currency": "ARS",
    "discount_percentage": 11.8
  },
  "market": "coto",
  "market_product_id": "12345",
  "url": "https://www.cotodigital3.com.ar/producto/12345",
  "image_url": "https://static.cotodigital3.com.ar/...",
  "availability": "in_stock",
  "promotions": [
    {
      "type": "discount",
      "description": "15% off"
    }
  ],
  "description": "Gaseosa Coca Cola 2.25L",
  "weight": "2.25L",
  "search_keywords": ["coca", "cola", "gaseosa", "bebida"],
  "scraped_at": "2024-01-01T00:00:00Z",
  "last_updated": "2024-01-01T00:00:00Z"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "detail": "Invalid query parameter"
}
```

### 404 Not Found
```json
{
  "detail": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- **Rate:** 100 requests per minute per IP
- **Headers:** Rate limit information in response headers
- **Exceeded:** HTTP 429 Too Many Requests

## CORS

Cross-Origin Resource Sharing (CORS) is enabled for all origins in development. Configure appropriately for production.

## OpenAPI Documentation

Interactive API documentation is available at:
- **Swagger UI:** http://localhost:8000/docs
- **ReDoc:** http://localhost:8000/redoc
- **OpenAPI JSON:** http://localhost:8000/openapi.json

## SDK Examples

### Python
```python
import requests

# Search for products
response = requests.get(
    "http://localhost:8000/api/v1/products/search",
    params={"q": "coca cola", "market": "coto"}
)
products = response.json()

# Compare prices
response = requests.get(
    "http://localhost:8000/api/v1/products/price-comparison",
    params={"product_name": "coca cola"}
)
comparison = response.json()
```

### JavaScript
```javascript
// Fetch products
const response = await fetch(
  'http://localhost:8000/api/v1/products?market=jumbo&limit=10'
);
const products = await response.json();

// Search products
const searchResponse = await fetch(
  'http://localhost:8000/api/v1/products/search?q=leche&category=Lácteos'
);
const searchResults = await searchResponse.json();
```

### cURL
```bash
# Get market statistics
curl -X GET "http://localhost:8000/api/v1/markets/coto/stats"

# Search with filters
curl -X GET "http://localhost:8000/api/v1/products/search?q=arroz&min_price=100&max_price=500"

# Get products with promotions
curl -X GET "http://localhost:8000/api/v1/products/promotions?market=carrefour"
```
