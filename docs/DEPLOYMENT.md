# Deployment Guide

This guide covers deploying the Argentine Supermarket Scraper in various environments.

## Prerequisites

- <PERSON><PERSON> and Docker Compose
- At least 4GB RAM
- 10GB free disk space
- Internet connection for scraping

## Quick Start (Development)

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd argentine-supermarket-scraper
   cp .env.example .env
   ```

2. **Configure environment**
   Edit `.env` file with your settings:
   ```bash
   # Database
   MONGO_ROOT_USERNAME=admin
   MONGO_ROOT_PASSWORD=your_secure_password
   
   # Scraping
   RATE_LIMIT_DELAY=2
   HEADLESS_BROWSER=true
   
   # Enable/disable markets
   COTO_ENABLED=true
   CARREFOUR_ENABLED=true
   ```

3. **Start services**
   ```bash
   docker-compose up -d
   ```

4. **Verify deployment**
   ```bash
   curl http://localhost:8000/api/v1/health
   ```

## Production Deployment

### 1. Environment Configuration

Create production `.env` file:
```bash
# Security
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=very_secure_password_here
API_HOST=0.0.0.0
API_PORT=8000

# Performance
RATE_LIMIT_DELAY=3
MAX_RETRIES=5
TIMEOUT=60
HEADLESS_BROWSER=true

# Scheduling
SCRAPING_SCHEDULE=0 */6 * * *  # Every 6 hours
DATA_RETENTION_DAYS=90

# Monitoring
LOG_LEVEL=INFO
ENABLE_NOTIFICATIONS=true
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
```

### 2. Docker Compose Production

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  mongodb:
    restart: always
    volumes:
      - /data/mongodb:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}

  api:
    restart: always
    environment:
      - LOG_LEVEL=INFO
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  scraper:
    restart: always
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  redis:
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - /data/redis:/data
```

### 3. Deploy to Production

```bash
# Build and deploy
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Check logs
docker-compose logs -f

# Monitor health
curl http://your-server:8000/api/v1/health/detailed
```

## Cloud Deployment

### AWS Deployment

1. **EC2 Instance Setup**
   ```bash
   # Launch EC2 instance (t3.large recommended)
   # Install Docker and Docker Compose
   sudo yum update -y
   sudo yum install -y docker
   sudo service docker start
   sudo usermod -a -G docker ec2-user
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **Security Groups**
   - Allow inbound traffic on port 8000 (API)
   - Allow SSH access (port 22)
   - Restrict MongoDB port 27017 to internal access only

3. **EBS Volume for Data**
   ```bash
   # Mount EBS volume for persistent data
   sudo mkfs -t xfs /dev/xvdf
   sudo mkdir /data
   sudo mount /dev/xvdf /data
   sudo chown ec2-user:ec2-user /data
   ```

### Google Cloud Platform

1. **Compute Engine Setup**
   ```bash
   # Create VM instance
   gcloud compute instances create supermarket-scraper \
     --machine-type=e2-standard-2 \
     --image-family=ubuntu-2004-lts \
     --image-project=ubuntu-os-cloud \
     --boot-disk-size=50GB
   ```

2. **Firewall Rules**
   ```bash
   gcloud compute firewall-rules create allow-api \
     --allow tcp:8000 \
     --source-ranges 0.0.0.0/0 \
     --description "Allow API access"
   ```

### DigitalOcean

1. **Droplet Creation**
   - Choose Ubuntu 20.04 LTS
   - Minimum 4GB RAM, 2 vCPUs
   - Add block storage for data persistence

2. **Setup Script**
   ```bash
   #!/bin/bash
   apt update && apt upgrade -y
   apt install -y docker.io docker-compose
   systemctl enable docker
   systemctl start docker
   usermod -aG docker $USER
   ```

## Monitoring and Maintenance

### 1. Health Monitoring

Create monitoring script:
```bash
#!/bin/bash
# health_check.sh

API_URL="http://localhost:8000/api/v1/health"
SLACK_WEBHOOK="your_slack_webhook_url"

response=$(curl -s -o /dev/null -w "%{http_code}" $API_URL)

if [ $response != "200" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🚨 Supermarket Scraper API is down!"}' \
        $SLACK_WEBHOOK
fi
```

### 2. Log Management

Setup log rotation:
```bash
# /etc/logrotate.d/docker-containers
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
```

### 3. Backup Strategy

Database backup script:
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/data/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
docker exec supermarket_mongodb mongodump --out /data/backup/$DATE

# Compress backup
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz -C /data/backup $DATE

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/backup_$DATE.tar.gz s3://your-backup-bucket/
```

### 4. Performance Tuning

MongoDB optimization:
```javascript
// Connect to MongoDB and run these commands
use supermarket_data

// Create compound indexes for better performance
db.products.createIndex({ "market": 1, "category": 1, "price.current": 1 })
db.products.createIndex({ "scraped_at": -1, "market": 1 })

// Enable profiling for slow queries
db.setProfilingLevel(1, { slowms: 100 })
```

## Scaling

### Horizontal Scaling

1. **Multiple Scraper Instances**
   ```yaml
   # docker-compose.scale.yml
   scraper:
     deploy:
       replicas: 3
   ```

2. **Load Balancer for API**
   ```yaml
   nginx:
     image: nginx:alpine
     ports:
       - "80:80"
     volumes:
       - ./nginx.conf:/etc/nginx/nginx.conf
     depends_on:
       - api
   ```

### Vertical Scaling

Increase resources in docker-compose:
```yaml
services:
  scraper:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
```

## Troubleshooting

### Common Issues

1. **Scraper fails with Chrome errors**
   ```bash
   # Check Chrome installation in container
   docker exec supermarket_scraper google-chrome --version
   
   # Restart scraper service
   docker-compose restart scraper
   ```

2. **MongoDB connection issues**
   ```bash
   # Check MongoDB logs
   docker-compose logs mongodb
   
   # Test connection
   docker exec supermarket_mongodb mongo --eval "db.adminCommand('ping')"
   ```

3. **API not responding**
   ```bash
   # Check API logs
   docker-compose logs api
   
   # Restart API service
   docker-compose restart api
   ```

### Performance Issues

1. **Slow scraping**
   - Increase `RATE_LIMIT_DELAY` to avoid being blocked
   - Check network connectivity
   - Monitor CPU and memory usage

2. **Database performance**
   - Check index usage: `db.products.explain().find({...})`
   - Monitor slow queries in MongoDB logs
   - Consider sharding for large datasets

### Security Considerations

1. **Network Security**
   - Use firewall to restrict access
   - Enable SSL/TLS for API endpoints
   - Use VPN for administrative access

2. **Data Security**
   - Encrypt MongoDB data at rest
   - Use strong passwords
   - Regular security updates

3. **API Security**
   - Implement rate limiting
   - Add authentication for sensitive endpoints
   - Monitor for unusual access patterns

## Maintenance Schedule

### Daily
- Check service health
- Monitor disk space
- Review error logs

### Weekly
- Update Docker images
- Backup database
- Review scraping statistics

### Monthly
- Security updates
- Performance review
- Capacity planning

### Quarterly
- Full system backup
- Disaster recovery testing
- Security audit
