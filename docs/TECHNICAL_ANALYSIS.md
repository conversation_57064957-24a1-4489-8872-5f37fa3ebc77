# Technical Analysis of Argentine Supermarket Websites

This document provides a detailed technical analysis of the target supermarket websites and the scraping strategies implemented.

## Website Analysis Summary

### 1. Coto Digital (cotodigital3.com.ar)

**Technology Stack:**
- Angular SPA (Single Page Application)
- VTEX Commerce Platform
- Dynamic content loading with JavaScript

**Challenges:**
- No robots.txt file found
- Heavy JavaScript rendering requires Selenium
- Dynamic product loading with pagination
- Anti-bot measures with reCAPTCHA

**Scraping Strategy:**
- Selenium WebDriver with Chrome
- Wait for dynamic content to load
- Handle pagination through JavaScript clicks
- Extract data from rendered DOM elements

**Key Selectors:**
```css
.vtex-store-components-3-x-productBrand          /* Product name */
.vtex-product-price-1-x-sellingPrice             /* Current price */
.vtex-product-summary-2-x-productBrandName       /* Brand */
.vtex-store-components-3-x-productImageTag       /* Product image */
```

### 2. Carrefour (carrefour.com.ar)

**Technology Stack:**
- VTEX Commerce Platform
- Server-side rendering with some dynamic elements
- RESTful API endpoints

**Challenges:**
- Mixed rendering (SSR + client-side)
- Rate limiting on API endpoints
- Complex pagination structure

**Scraping Strategy:**
- Hybrid approach: HTTP requests + Selenium fallback
- BeautifulSoup for static content parsing
- Selenium for dynamic elements when needed

**Key Selectors:**
```css
.vtex-store-components-3-x-productBrand          /* Product name */
.vtex-product-price-1-x-sellingPrice             /* Price */
.product-item a                                  /* Product links */
```

### 3. Jumbo, Disco, Vea (VTEX Platform)

**Technology Stack:**
- VTEX Commerce Platform (shared architecture)
- Similar DOM structure across all three
- Server-side rendering with progressive enhancement

**Challenges:**
- Similar robots.txt restrictions
- Shared platform means similar anti-scraping measures
- Different category structures per site

**Scraping Strategy:**
- Unified VTEX scraper base class
- Site-specific configuration and selectors
- HTTP requests preferred for performance

**Common Selectors:**
```css
.vtex-product-summary-2-x-clearLink              /* Product links */
.vtex-store-components-3-x-productBrand          /* Product name */
.vtex-product-price-1-x-sellingPrice             /* Price */
```

## Robots.txt Analysis

### Carrefour, Jumbo, Disco, Vea
```
User-agent: *
Disallow: /img/*
Disallow: /account/*
Disallow: /login/*
Disallow: /checkout/*
Disallow: /busca/*
Disallow: /quick-view/*
```

**Compliance Strategy:**
- Respect all disallowed paths
- Focus on product catalog pages
- Avoid user account and checkout areas
- Implement robots.txt checking in base scraper

### Coto Digital
- No robots.txt file found (404 response)
- Proceed with standard ethical scraping practices

## Anti-Scraping Measures Detected

### 1. Rate Limiting
- **Detection:** HTTP 429 responses, connection timeouts
- **Mitigation:** Configurable delays between requests (2-5 seconds)
- **Implementation:** Rate limiting in base scraper class

### 2. User Agent Detection
- **Detection:** Different responses for bot user agents
- **Mitigation:** User agent rotation with realistic browser strings
- **Implementation:** fake-useragent library with rotation

### 3. JavaScript Challenges
- **Detection:** Content only loads with JavaScript enabled
- **Mitigation:** Selenium WebDriver for dynamic content
- **Implementation:** Headless Chrome with stealth options

### 4. CAPTCHA Systems
- **Detection:** reCAPTCHA on Coto Digital
- **Mitigation:** Slower request rates, session management
- **Implementation:** Extended delays and session persistence

## Data Extraction Patterns

### Product Information Schema
```json
{
  "name": "Product name",
  "price": {
    "current": 1234.56,
    "previous": 1500.00,
    "currency": "ARS",
    "discount_percentage": 17.7
  },
  "brand": "Brand name",
  "category": "Category",
  "market": "coto|carrefour|jumbo|disco|vea",
  "image_url": "https://...",
  "availability": "in_stock|out_of_stock|limited_stock",
  "promotions": [
    {
      "type": "discount",
      "description": "30% off"
    }
  ],
  "scraped_at": "2024-01-01T00:00:00Z"
}
```

### Price Extraction Challenges
- **Multiple formats:** $1.234,56 vs 1234.56 vs $1,234.56
- **Currency symbols:** $ symbol placement varies
- **Decimal separators:** Comma vs period usage
- **Solution:** Robust regex patterns with format normalization

### Category Mapping
Each site uses different category structures:
- **Coto:** "Almacén", "Bebidas", "Frescos"
- **Carrefour:** "almacen", "bebidas", "carnes"
- **VTEX sites:** "almacen", "lacteos-y-refrigerados"

**Normalization Strategy:**
- Site-specific category mappings
- Standardized category names in database
- Breadcrumb extraction for accurate categorization

## Performance Considerations

### Scraping Speed
- **Coto (Selenium):** ~2-3 products/minute
- **Others (HTTP):** ~10-15 products/minute
- **Bottleneck:** Rate limiting and page load times

### Resource Usage
- **Memory:** ~500MB per Selenium instance
- **CPU:** Moderate during HTML parsing
- **Network:** Bandwidth depends on image downloading

### Scalability
- **Horizontal:** Multiple scraper instances per market
- **Vertical:** Increased rate limits with proxy rotation
- **Database:** MongoDB with proper indexing

## Error Handling Strategy

### Common Errors
1. **Network timeouts:** Retry with exponential backoff
2. **Element not found:** Multiple selector fallbacks
3. **Rate limiting:** Increased delays and session rotation
4. **CAPTCHA:** Manual intervention alerts
5. **Site structure changes:** Graceful degradation

### Monitoring
- Session tracking in database
- Error logging with context
- Success rate metrics per market
- Alert system for critical failures

## Legal and Ethical Considerations

### Compliance Measures
- **Robots.txt respect:** Automated checking before requests
- **Rate limiting:** Conservative request frequencies
- **Data usage:** Research and comparison purposes only
- **Attribution:** Proper source attribution in data

### Best Practices Implemented
- User-Agent identification as research bot
- Respectful crawling delays
- No personal data collection
- Public data only (product catalogs)
- Terms of service review and compliance
