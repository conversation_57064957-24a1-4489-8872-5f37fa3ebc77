import React from 'react';
import {
  ChartBarIcon,
  CpuChipIcon,
  CircleStackIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { SystemMetrics } from '../../types';

interface PerformanceMonitoringProps {
  metrics: SystemMetrics;
}

const PerformanceMonitoring: React.FC<PerformanceMonitoringProps> = ({
  metrics,
}) => {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressBarColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'bg-green-500';
    if (value <= thresholds.warning) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const MetricCard = ({ 
    title, 
    value, 
    unit, 
    icon: Icon, 
    trend, 
    trendValue,
    color = 'text-gray-900' 
  }: {
    title: string;
    value: string | number;
    unit?: string;
    icon: React.ComponentType<any>;
    trend?: 'up' | 'down';
    trendValue?: string;
    color?: string;
  }) => (
    <div className="card">
      <div className="card-body">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>
              {value}
              {unit && <span className="text-lg font-normal text-gray-500 ml-1">{unit}</span>}
            </p>
            {trend && trendValue && (
              <div className="flex items-center mt-1">
                {trend === 'up' ? (
                  <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  {trendValue}
                </span>
              </div>
            )}
          </div>
          <div className="p-3 bg-gray-50 rounded-lg">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
        </div>
      </div>
    </div>
  );

  const ProgressBar = ({ 
    label, 
    value, 
    max = 100, 
    thresholds = { good: 50, warning: 80 } 
  }: {
    label: string;
    value: number;
    max?: number;
    thresholds?: { good: number; warning: number };
  }) => {
    const percentage = (value / max) * 100;
    const color = getProgressBarColor(value, thresholds);
    const textColor = getPerformanceColor(value, thresholds);

    return (
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="font-medium text-gray-700">{label}</span>
          <span className={`font-medium ${textColor}`}>
            {value.toFixed(1)}{max === 100 ? '%' : ` / ${max}`}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${color}`}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">Performance Monitoring</h2>
        <p className="mt-1 text-sm text-gray-500">
          Real-time system metrics and performance indicators
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Products Scraped (24h)"
          value={formatNumber(metrics.productsScrapedLast24h)}
          icon={ChartBarIcon}
          trend="up"
          trendValue="+12%"
          color="text-blue-600"
        />
        <MetricCard
          title="Success Rate"
          value={metrics.successRate.toFixed(1)}
          unit="%"
          icon={ArrowTrendingUpIcon}
          trend="up"
          trendValue="+2.1%"
          color={getPerformanceColor(100 - metrics.successRate, { good: 5, warning: 15 })}
        />
        <MetricCard
          title="Avg Response Time"
          value={metrics.averageResponseTime.toFixed(0)}
          unit="ms"
          icon={ClockIcon}
          trend="down"
          trendValue="-50ms"
          color={getPerformanceColor(metrics.averageResponseTime, { good: 1000, warning: 3000 })}
        />
        <MetricCard
          title="Database Size"
          value={metrics.databaseSize}
          icon={CircleStackIcon}
          trend="up"
          trendValue="+1.2GB"
          color="text-purple-600"
        />
      </div>

      {/* System Resources */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <CpuChipIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">System Resources</h3>
          </div>
        </div>
        <div className="card-body space-y-6">
          <ProgressBar
            label="CPU Usage"
            value={metrics.cpuUsage}
            thresholds={{ good: 50, warning: 80 }}
          />
          <ProgressBar
            label="Memory Usage"
            value={metrics.memoryUsage}
            thresholds={{ good: 60, warning: 85 }}
          />
          <ProgressBar
            label="Database Storage"
            value={75} // Mock value
            thresholds={{ good: 70, warning: 90 }}
          />
        </div>
      </div>

      {/* Historical Charts Placeholder */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Historical Performance</h3>
        </div>
        <div className="card-body">
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Charts Coming Soon</h3>
            <p className="text-gray-500 mb-6">
              Interactive performance charts will be available in the next version.
            </p>
            <div className="space-y-2 text-sm text-gray-400">
              <p>Planned charts:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Products scraped over time</li>
                <li>Success rate trends</li>
                <li>Response time distribution</li>
                <li>Error rate by market</li>
                <li>System resource usage</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-sm font-medium text-gray-900">Active Scrapers</h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              {['Coto', 'Carrefour', 'Jumbo'].map((market, index) => (
                <div key={market} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{market}</span>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                    <span className="text-xs text-green-600">Running</span>
                  </div>
                </div>
              ))}
              {['Disco', 'Vea'].map((market) => (
                <div key={market} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{market}</span>
                  <div className="flex items-center">
                    <div className="h-2 w-2 bg-gray-400 rounded-full mr-2" />
                    <span className="text-xs text-gray-600">Idle</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-sm font-medium text-gray-900">Recent Alerts</h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="h-2 w-2 bg-yellow-400 rounded-full mt-2" />
                <div>
                  <p className="text-sm text-gray-700">High response time detected</p>
                  <p className="text-xs text-gray-500">Carrefour - 2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="h-2 w-2 bg-green-400 rounded-full mt-2" />
                <div>
                  <p className="text-sm text-gray-700">Scraping completed successfully</p>
                  <p className="text-xs text-gray-500">Coto - 15 minutes ago</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="h-2 w-2 bg-red-400 rounded-full mt-2" />
                <div>
                  <p className="text-sm text-gray-700">Connection timeout</p>
                  <p className="text-xs text-gray-500">Jumbo - 1 hour ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Badge */}
      <div className="text-center">
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <ClockIcon className="h-4 w-4 mr-1" />
          Real-time Metrics Coming Soon - v2.0
        </span>
      </div>
    </div>
  );
};

export default PerformanceMonitoring;
