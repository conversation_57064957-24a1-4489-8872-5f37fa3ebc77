import React, { useState } from 'react';
import {
  CogIcon,
  ServerIcon,
  ClockIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface SystemConfigurationProps {
  onSaveConfig: (config: any) => void;
}

const SystemConfiguration: React.FC<SystemConfigurationProps> = ({
  onSaveConfig,
}) => {
  const [config, setConfig] = useState({
    database: {
      connectionString: 'mongodb://localhost:27017/supermarket_scraper',
      status: 'connected',
    },
    api: {
      rateLimitPerMinute: 60,
      timeout: 30,
    },
    scheduler: {
      cronExpression: '0 2 * * *', // Daily at 2 AM
      timezone: 'America/Argentina/Buenos_Aires',
    },
    markets: {
      coto: {
        maxPages: 10,
        delayBetweenRequests: 2000,
        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',
      },
      carrefour: {
        maxPages: 15,
        delayBetweenRequests: 1500,
        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',
      },
      jumbo: {
        maxPages: 12,
        delayBetweenRequests: 2000,
        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',
      },
      disco: {
        maxPages: 8,
        delayBetweenRequests: 2500,
        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',
      },
      vea: {
        maxPages: 10,
        delayBetweenRequests: 2000,
        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',
      },
    },
  });

  const [hasChanges, setHasChanges] = useState(false);

  const updateConfig = (path: string[], value: any) => {
    const newConfig = { ...config };
    let current: any = newConfig;

    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }
    current[path[path.length - 1]] = value;

    setConfig(newConfig);
    setHasChanges(true);
  };

  const handleSave = () => {
    onSaveConfig(config);
    setHasChanges(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    }
  };

  const marketDisplayNames: Record<string, string> = {
    coto: 'Coto Digital',
    carrefour: 'Carrefour',
    jumbo: 'Jumbo',
    disco: 'Disco',
    vea: 'Vea',
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">System Configuration</h2>
          <p className="mt-1 text-sm text-gray-500">
            Configure database, API settings, and scraper parameters
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={handleSave}
            disabled={!hasChanges}
            className={`btn ${hasChanges ? 'btn-primary' : 'btn-outline'}`}
          >
            <CogIcon className="h-4 w-4 mr-2" />
            Save Configuration
          </button>
        </div>
      </div>

      {/* Database Configuration */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <ServerIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Database Configuration</h3>
          </div>
        </div>
        <div className="card-body space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Connection String
              </label>
              <div className="flex items-center">
                {getStatusIcon(config.database.status)}
                <span className="ml-1 text-sm text-gray-600 capitalize">
                  {config.database.status}
                </span>
              </div>
            </div>
            <input
              type="text"
              value={config.database.connectionString}
              onChange={(e) => updateConfig(['database', 'connectionString'], e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              placeholder="mongodb://localhost:27017/supermarket_scraper"
            />
          </div>
        </div>
      </div>

      {/* API Configuration */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <ShieldCheckIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">API Configuration</h3>
          </div>
        </div>
        <div className="card-body space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rate Limit (requests/minute)
              </label>
              <input
                type="range"
                min="10"
                max="200"
                value={config.api.rateLimitPerMinute}
                onChange={(e) => updateConfig(['api', 'rateLimitPerMinute'], parseInt(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>10</span>
                <span className="font-medium">{config.api.rateLimitPerMinute}</span>
                <span>200</span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timeout (seconds)
              </label>
              <input
                type="number"
                value={config.api.timeout}
                onChange={(e) => updateConfig(['api', 'timeout'], parseInt(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                min="5"
                max="300"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Scheduler Configuration */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <ClockIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Scheduler Configuration</h3>
          </div>
        </div>
        <div className="card-body space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cron Expression
              </label>
              <input
                type="text"
                value={config.scheduler.cronExpression}
                onChange={(e) => updateConfig(['scheduler', 'cronExpression'], e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="0 2 * * *"
              />
              <p className="text-xs text-gray-500 mt-1">
                Current: Daily at 2:00 AM
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timezone
              </label>
              <select
                value={config.scheduler.timezone}
                onChange={(e) => updateConfig(['scheduler', 'timezone'], e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="America/Argentina/Buenos_Aires">Buenos Aires</option>
                <option value="UTC">UTC</option>
                <option value="America/New_York">New York</option>
                <option value="Europe/London">London</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Market-Specific Configuration */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Market-Specific Parameters</h3>
        </div>
        <div className="card-body">
          <div className="space-y-6">
            {Object.entries(config.markets).map(([market, marketConfig]) => (
              <div key={market} className="border border-gray-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">
                  {marketDisplayNames[market]}
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Max Pages
                    </label>
                    <input
                      type="number"
                      value={marketConfig.maxPages}
                      onChange={(e) => updateConfig(['markets', market, 'maxPages'], parseInt(e.target.value))}
                      className="w-full text-sm border border-gray-300 rounded-md px-2 py-1"
                      min="1"
                      max="100"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Delay (ms)
                    </label>
                    <input
                      type="number"
                      value={marketConfig.delayBetweenRequests}
                      onChange={(e) => updateConfig(['markets', market, 'delayBetweenRequests'], parseInt(e.target.value))}
                      className="w-full text-sm border border-gray-300 rounded-md px-2 py-1"
                      min="500"
                      max="10000"
                      step="500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      User Agent
                    </label>
                    <input
                      type="text"
                      value={marketConfig.userAgent}
                      onChange={(e) => updateConfig(['markets', market, 'userAgent'], e.target.value)}
                      className="w-full text-sm border border-gray-300 rounded-md px-2 py-1"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Coming Soon Badge */}
      <div className="text-center">
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <ClockIcon className="h-4 w-4 mr-1" />
          Configuration Persistence Coming Soon - v2.0
        </span>
      </div>
    </div>
  );
};

export default SystemConfiguration;
