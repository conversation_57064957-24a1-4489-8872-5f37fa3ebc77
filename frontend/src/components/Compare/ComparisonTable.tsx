import React from 'react';
import {
  CheckCircleIcon,
  XCircleIcon,
  ArrowTopRightOnSquareIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { PriceComparison } from '../../types';
import { formatDistanceToNow } from 'date-fns';

interface ComparisonTableProps {
  comparison: PriceComparison;
}

const ComparisonTable: React.FC<ComparisonTableProps> = ({ comparison }) => {
  const getMarketColor = (marketName: string) => {
    const colors: Record<string, string> = {
      coto: 'bg-red-500',
      carrefour: 'bg-blue-600',
      jumbo: 'bg-orange-500',
      disco: 'bg-green-600',
      vea: 'bg-purple-600',
    };
    return colors[marketName.toLowerCase()] || 'bg-gray-500';
  };

  const sortedPrices = [...comparison.prices].sort((a, b) => {
    // Sort by availability first, then by price
    if (a.availability !== b.availability) {
      return a.availability ? -1 : 1;
    }
    return a.price - b.price;
  });

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900">Price Comparison Details</h3>
      </div>
      <div className="card-body p-0">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Market
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Discount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Availability
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Promotion
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Updated
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedPrices.map((price, index) => {
                const isBestPrice = price.market === comparison.best_price.market && price.availability;
                const hasDiscount = price.original_price && price.original_price > price.price;
                const savings = comparison.best_price.savings && price.market !== comparison.best_price.market
                  ? price.price - comparison.best_price.price
                  : 0;

                return (
                  <tr 
                    key={price.market}
                    className={`${isBestPrice ? 'bg-success-50 border-l-4 border-success-400' : ''} hover:bg-gray-50`}
                  >
                    {/* Market */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <div className={`h-3 w-3 rounded-full ${getMarketColor(price.market)}`}></div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900 uppercase">
                              {price.market}
                            </span>
                            {isBestPrice && (
                              <StarIcon className="h-4 w-4 text-warning-500 fill-current" />
                            )}
                          </div>
                          {isBestPrice && (
                            <span className="text-xs text-success-600 font-medium">
                              Best Price
                            </span>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* Price */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="text-sm font-semibold text-gray-900">
                          ${price.price.toFixed(2)}
                        </div>
                        {hasDiscount && (
                          <div className="text-xs text-gray-500 line-through">
                            ${price.original_price!.toFixed(2)}
                          </div>
                        )}
                        {savings > 0 && (
                          <div className="text-xs text-error-600">
                            +${savings.toFixed(2)} vs best
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Discount */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      {price.discount_percentage && price.discount_percentage > 0 ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                          -{price.discount_percentage}%
                        </span>
                      ) : (
                        <span className="text-sm text-gray-400">-</span>
                      )}
                    </td>

                    {/* Availability */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {price.availability ? (
                          <>
                            <CheckCircleIcon className="h-4 w-4 text-success-500" />
                            <span className="text-sm text-success-600 font-medium">Available</span>
                          </>
                        ) : (
                          <>
                            <XCircleIcon className="h-4 w-4 text-error-500" />
                            <span className="text-sm text-error-600 font-medium">Out of Stock</span>
                          </>
                        )}
                      </div>
                    </td>

                    {/* Promotion */}
                    <td className="px-6 py-4">
                      {price.promotion ? (
                        <div className="max-w-xs">
                          <span className="text-xs text-warning-700 bg-warning-50 px-2 py-1 rounded border border-warning-200">
                            🎉 {price.promotion}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">-</span>
                      )}
                    </td>

                    {/* Updated */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(price.updated_at), { addSuffix: true })}
                      </span>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      {price.product_url ? (
                        <a
                          href={price.product_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700"
                        >
                          View Product
                          <ArrowTopRightOnSquareIcon className="ml-1 h-3 w-3" />
                        </a>
                      ) : (
                        <span className="text-sm text-gray-400">-</span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ComparisonTable;
