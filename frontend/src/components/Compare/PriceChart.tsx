import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { PriceComparison } from '../../types';

interface PriceChartProps {
  comparison: PriceComparison;
  height?: number;
}

const PriceChart: React.FC<PriceChartProps> = ({ comparison, height = 300 }) => {
  const getMarketColor = (marketName: string) => {
    const colors: Record<string, string> = {
      coto: '#ef4444',
      carrefour: '#2563eb',
      jumbo: '#f97316',
      disco: '#16a34a',
      vea: '#9333ea',
    };
    return colors[marketName.toLowerCase()] || '#6b7280';
  };

  const chartData = comparison.prices
    .filter(price => price.availability) // Only show available products
    .map(price => ({
      market: price.market.toUpperCase(),
      price: price.price,
      originalPrice: price.original_price,
      discount: price.discount_percentage || 0,
      isBestPrice: price.market === comparison.best_price.market,
      color: getMarketColor(price.market),
      promotion: price.promotion,
    }))
    .sort((a, b) => a.price - b.price); // Sort by price ascending

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">
              <span className="text-gray-600">Price: </span>
              <span className="font-semibold text-gray-900">${data.price.toFixed(2)}</span>
            </p>
            {data.originalPrice && data.originalPrice > data.price && (
              <p className="text-sm">
                <span className="text-gray-600">Original: </span>
                <span className="line-through text-gray-500">${data.originalPrice.toFixed(2)}</span>
                <span className="ml-2 text-success-600 font-medium">
                  -{data.discount}%
                </span>
              </p>
            )}
            {data.promotion && (
              <p className="text-xs text-warning-700 bg-warning-50 px-2 py-1 rounded">
                🎉 {data.promotion}
              </p>
            )}
            {data.isBestPrice && (
              <p className="text-xs text-success-700 bg-success-50 px-2 py-1 rounded font-medium">
                ⭐ Best Price
              </p>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  const formatYAxisTick = (value: number) => `$${value.toFixed(0)}`;

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
          <XAxis 
            dataKey="market" 
            tick={{ fontSize: 12 }}
            stroke="#6b7280"
          />
          <YAxis 
            tickFormatter={formatYAxisTick}
            tick={{ fontSize: 12 }}
            stroke="#6b7280"
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="price" 
            radius={[4, 4, 0, 0]}
            stroke="#ffffff"
            strokeWidth={1}
          >
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.color}
                opacity={entry.isBestPrice ? 1 : 0.8}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
      
      {/* Chart Legend */}
      <div className="mt-4 flex flex-wrap justify-center gap-4">
        {chartData.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded"
              style={{ backgroundColor: item.color }}
            ></div>
            <span className="text-sm text-gray-600">
              {item.market} - ${item.price.toFixed(2)}
              {item.isBestPrice && (
                <span className="ml-1 text-success-600 font-medium">⭐</span>
              )}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PriceChart;
