import React, { useState, useEffect } from 'react';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { HealthStatus as HealthStatusType } from '../../types';
import { apiClient } from '../../services/api';
import { formatDistanceToNow } from 'date-fns';

const HealthStatus: React.FC = () => {
  const [health, setHealth] = useState<HealthStatusType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHealth = async () => {
      try {
        setLoading(true);
        const healthData = await apiClient.health.getDetailedHealth();
        setHealth(healthData);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch health status');
      } finally {
        setLoading(false);
      }
    };

    fetchHealth();
    
    // Refresh health status every 30 seconds
    const interval = setInterval(fetchHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return <CheckCircleIcon className="h-5 w-5 text-success-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-warning-500" />;
      case 'error':
      case 'unhealthy':
        return <XCircleIcon className="h-5 w-5 text-error-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return 'text-success-600 bg-success-50 border-success-200';
      case 'warning':
        return 'text-warning-600 bg-warning-50 border-warning-200';
      case 'error':
      case 'unhealthy':
        return 'text-error-600 bg-error-50 border-error-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">System Health</h3>
        </div>
        <div className="card-body">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">System Health</h3>
        </div>
        <div className="card-body">
          <div className="flex items-center space-x-2 text-error-600">
            <XCircleIcon className="h-5 w-5" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  if (!health) return null;

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">System Health</h3>
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(health.status)}`}>
            {getStatusIcon(health.status)}
            <span className="ml-1 capitalize">{health.status}</span>
          </div>
        </div>
      </div>
      <div className="card-body space-y-4">
        {/* Database Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Database</span>
          <div className="flex items-center space-x-2">
            {health.database?.status === 'connected' ? (
              <CheckCircleIcon className="h-4 w-4 text-success-500" />
            ) : (
              <XCircleIcon className="h-4 w-4 text-error-500" />
            )}
            <span className="text-sm text-gray-600 capitalize">{health.database?.status || 'unknown'}</span>
          </div>
        </div>

        {/* Total Products */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Total Products</span>
          <span className="text-sm font-semibold text-gray-900">
            {health?.database?.total_products?.toLocaleString() || '0'}
          </span>
        </div>

        {/* Last Updated */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Last Updated</span>
          <span className="text-sm text-gray-600">
            {health?.timestamp ? formatDistanceToNow(new Date(health.timestamp), { addSuffix: true }) : 'Unknown'}
          </span>
        </div>

        {/* Products by Market */}
        {health.database?.products_by_market && Object.keys(health.database.products_by_market).length > 0 && (
          <div className="pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Products by Market</h4>
            <div className="space-y-2">
              {Object.entries(health.database.products_by_market).map(([market, count]) => (
                <div key={market} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{market}</span>
                  <span className="text-sm font-medium text-gray-900">
                    {(count || 0).toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HealthStatus;
