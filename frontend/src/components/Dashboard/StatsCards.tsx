import React, { useState, useEffect } from 'react';
import {
  ShoppingCartIcon,
  BuildingStorefrontIcon,
  TagIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { apiClient } from '../../services/api';

interface StatsData {
  total_products: number;
  total_markets: number;
  total_categories: number;
  last_updated: string;
  products_by_market: Record<string, number>;
  products_by_category: Record<string, number>;
}

const StatsCards: React.FC = () => {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const statsData = await apiClient.stats.getOverallStats();
        setStats(statsData);
        setError(null);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const cards = [
    {
      title: 'Total Products',
      value: stats?.total_products || 0,
      icon: ShoppingCartIcon,
      color: 'text-primary-600 bg-primary-50',
      change: '+12% from last week',
      changeType: 'positive' as const,
    },
    {
      title: 'Active Markets',
      value: stats?.total_markets || 0,
      icon: BuildingStorefrontIcon,
      color: 'text-success-600 bg-success-50',
      change: 'All markets online',
      changeType: 'neutral' as const,
    },
    {
      title: 'Categories',
      value: stats?.total_categories || 0,
      icon: TagIcon,
      color: 'text-warning-600 bg-warning-50',
      change: '+3 new categories',
      changeType: 'positive' as const,
    },
    {
      title: 'Last Updated',
      value: stats?.last_updated ? new Date(stats.last_updated).toLocaleDateString() : 'N/A',
      icon: ClockIcon,
      color: 'text-gray-600 bg-gray-50',
      change: 'Auto-sync enabled',
      changeType: 'neutral' as const,
    },
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="card animate-pulse">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gray-200 rounded"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-body text-center py-8">
          <p className="text-error-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card, index) => (
        <div key={index} className="card hover:shadow-md transition-shadow duration-200">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-2 rounded-lg ${card.color}`}>
                  <card.icon className="h-6 w-6" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {card.title}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {typeof card.value === 'number' 
                        ? card.value.toLocaleString() 
                        : card.value
                      }
                    </div>
                  </dd>
                  <dd className="mt-1">
                    <div className={`flex items-baseline text-sm ${
                      card.changeType === 'positive'
                        ? 'text-success-600'
                        : 'text-gray-500'
                    }`}>
                      {card.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatsCards;
