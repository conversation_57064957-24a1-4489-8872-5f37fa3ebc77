import React from 'react';
import { Link } from 'react-router-dom';
import {
  BuildingStorefrontIcon,
  TagIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { Product } from '../../types';
import { formatDistanceToNow } from 'date-fns';

interface ProductCardProps {
  product: Product;
  showMarket?: boolean;
  showCategory?: boolean;
  onCompare?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  showMarket = true,
  showCategory = true,
  onCompare,
}) => {
  const getMarketColor = (marketName: string) => {
    const colors: Record<string, string> = {
      coto: 'bg-red-500',
      carrefour: 'bg-blue-600',
      jumbo: 'bg-orange-500',
      disco: 'bg-green-600',
      vea: 'bg-purple-600',
    };
    return colors[marketName.toLowerCase()] || 'bg-gray-500';
  };

  const hasDiscount = product.price.original && product.price.original > product.price.current;
  const discountPercentage = hasDiscount
    ? Math.round(((product.price.original! - product.price.current) / product.price.original!) * 100)
    : product.discount_percentage;

  return (
    <div className="card hover:shadow-lg transition-shadow duration-200">
      <div className="card-body p-4">
        {/* Product Image */}
        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4">
          {product.image_url ? (
            <img
              src={product.image_url}
              alt={product.name}
              className="h-full w-full object-cover object-center"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`${product.image_url ? 'hidden' : ''} flex items-center justify-center h-full`}>
            <BuildingStorefrontIcon className="h-12 w-12 text-gray-400" />
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-3">
          {/* Market and Availability */}
          <div className="flex items-center justify-between">
            {showMarket && (
              <div className="flex items-center space-x-2">
                <div className={`h-3 w-3 rounded-full ${getMarketColor(product.market)}`}></div>
                <span className="text-xs font-medium text-gray-600 uppercase">
                  {product.market}
                </span>
              </div>
            )}
            <div className="flex items-center space-x-1">
              {product.availability === 'in_stock' ? (
                <CheckCircleIcon className="h-4 w-4 text-success-500" />
              ) : (
                <ExclamationTriangleIcon className="h-4 w-4 text-warning-500" />
              )}
              <span className={`text-xs font-medium ${
                product.availability === 'in_stock' ? 'text-success-600' : 'text-warning-600'
              }`}>
                {product.availability === 'in_stock' ? 'Available' :
                 product.availability === 'out_of_stock' ? 'Out of Stock' :
                 product.availability === 'limited_stock' ? 'Limited Stock' : 'Unknown'}
              </span>
            </div>
          </div>

          {/* Product Name */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
              {product.name}
            </h3>
            {product.brand && (
              <p className="text-xs text-gray-500 mt-1">{product.brand}</p>
            )}
          </div>

          {/* Category */}
          {showCategory && (
            <div className="flex items-center space-x-1">
              <TagIcon className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-500">{product.category}</span>
              {product.subcategory && (
                <>
                  <span className="text-xs text-gray-400">•</span>
                  <span className="text-xs text-gray-500">{product.subcategory}</span>
                </>
              )}
            </div>
          )}

          {/* Price */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-lg font-bold text-gray-900">
                  ${product.price.current.toFixed(2)}
                </span>
                {product.unit && (
                  <span className="text-xs text-gray-500">/ {product.unit}</span>
                )}
              </div>
              {discountPercentage && discountPercentage > 0 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800">
                  -{discountPercentage}%
                </span>
              )}
            </div>
            
            {hasDiscount && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 line-through">
                  ${product.price.original!.toFixed(2)}
                </span>
                <span className="text-sm font-medium text-success-600">
                  Save ${(product.price.original! - product.price.current).toFixed(2)}
                </span>
              </div>
            )}
          </div>

          {/* Promotion */}
          {product.promotion && (
            <div className="p-2 bg-warning-50 border border-warning-200 rounded-md">
              <p className="text-xs text-warning-800 font-medium">
                🎉 {product.promotion}
              </p>
            </div>
          )}

          {/* Last Updated */}
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <ClockIcon className="h-3 w-3" />
            <span>
              Updated {formatDistanceToNow(new Date(product.last_updated), { addSuffix: true })}
            </span>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            {product.url && (
              <a
                href={product.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 btn btn-outline btn-sm"
              >
                View Product
              </a>
            )}
            {onCompare && (
              <button
                onClick={() => onCompare(product)}
                className="flex-1 btn btn-primary btn-sm"
              >
                Compare
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
