import React, { useState, useEffect } from 'react';
import {
  FunnelIcon,
  XMarkIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { SearchParams, Market, Category } from '../../types';
import { apiClient } from '../../services/api';

interface SearchFiltersProps {
  filters: SearchParams;
  onFiltersChange: (filters: SearchParams) => void;
  onClearFilters: () => void;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
}) => {
  const [markets, setMarkets] = useState<Market[]>([]);
  const [categories, setCategories] = useState<(Category | string)[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFilterData = async () => {
      try {
        setLoading(true);
        const [marketsData, categoriesData] = await Promise.all([
          apiClient.markets.getMarkets(),
          apiClient.categories.getCategories(),
        ]);
        setMarkets(marketsData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Failed to fetch filter data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFilterData();
  }, []);

  const handleFilterChange = (key: keyof SearchParams, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleArrayFilterChange = (key: keyof SearchParams, value: string, checked: boolean) => {
    const currentArray = (filters[key] as string[]) || [];
    const newArray = checked
      ? [...currentArray, value]
      : currentArray.filter(item => item !== value);
    
    handleFilterChange(key, newArray.length > 0 ? newArray : undefined);
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && 
    (Array.isArray(value) ? value.length > 0 : true)
  );

  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-500" />
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            {hasActiveFilters && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                Active
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <button
                onClick={onClearFilters}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear all
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="lg:hidden p-1 text-gray-500 hover:text-gray-700"
            >
              <ChevronDownIcon 
                className={`h-4 w-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
              />
            </button>
          </div>
        </div>
      </div>
      
      <div className={`card-body space-y-6 ${isExpanded ? 'block' : 'hidden lg:block'}`}>
        {/* Markets Filter */}
        <div>
          <label className="form-label">Markets</label>
          <div className="space-y-2">
            {markets.map((market) => (
              <div key={market.name} className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox"
                  checked={(filters.market || []).includes(market.name)}
                  onChange={(e) => handleArrayFilterChange('market', market.name, e.target.checked)}
                />
                <label className="ml-2 text-sm text-gray-700 cursor-pointer">
                  {market.display_name || market.name} ({(market.product_count || 0).toLocaleString()})
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Categories Filter */}
        <div>
          <label className="form-label">Categories</label>
          <div className="space-y-2 max-h-48 overflow-y-auto scrollbar-thin">
            {categories.map((category) => {
              const categoryName = typeof category === 'string' ? category : category.name;
              const displayName = typeof category === 'string' ? category : (category.display_name || category.name);
              const productCount = typeof category === 'string' ? 0 : (category.product_count || 0);

              return (
                <div key={categoryName} className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox"
                    checked={(filters.category || []).includes(categoryName)}
                    onChange={(e) => handleArrayFilterChange('category', categoryName, e.target.checked)}
                  />
                  <label className="ml-2 text-sm text-gray-700 cursor-pointer">
                    {displayName} ({productCount.toLocaleString()})
                  </label>
                </div>
              );
            })}
          </div>
        </div>

        {/* Price Range Filter */}
        <div>
          <label className="form-label">Price Range</label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <input
                type="number"
                placeholder="Min price"
                className="form-input"
                value={filters.min_price || ''}
                onChange={(e) => handleFilterChange('min_price', e.target.value ? Number(e.target.value) : undefined)}
              />
            </div>
            <div>
              <input
                type="number"
                placeholder="Max price"
                className="form-input"
                value={filters.max_price || ''}
                onChange={(e) => handleFilterChange('max_price', e.target.value ? Number(e.target.value) : undefined)}
              />
            </div>
          </div>
        </div>

        {/* Availability Filter */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="form-checkbox"
              checked={filters.availability || false}
              onChange={(e) => handleFilterChange('availability', e.target.checked || undefined)}
            />
            <span className="ml-2 text-sm text-gray-700">
              Only show available products
            </span>
          </label>
        </div>

        {/* Promotion Filter */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="form-checkbox"
              checked={filters.on_promotion || false}
              onChange={(e) => handleFilterChange('on_promotion', e.target.checked || undefined)}
            />
            <span className="ml-2 text-sm text-gray-700">
              Only show products on promotion
            </span>
          </label>
        </div>

        {/* Sort Options */}
        <div>
          <label className="form-label">Sort by</label>
          <select
            className="form-select"
            value={`${filters.sort_by || 'name'}_${filters.sort_order || 'asc'}`}
            onChange={(e) => {
              const [sort_by, sort_order] = e.target.value.split('_');
              handleFilterChange('sort_by', sort_by as any);
              handleFilterChange('sort_order', sort_order as any);
            }}
          >
            <option value="name_asc">Name (A-Z)</option>
            <option value="name_desc">Name (Z-A)</option>
            <option value="price_asc">Price (Low to High)</option>
            <option value="price_desc">Price (High to Low)</option>
            <option value="discount_desc">Discount (High to Low)</option>
            <option value="updated_at_desc">Recently Updated</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default SearchFilters;
