@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* Status Indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply status-indicator bg-success-100 text-success-800;
  }
  
  .status-warning {
    @apply status-indicator bg-warning-100 text-warning-800;
  }
  
  .status-error {
    @apply status-indicator bg-error-100 text-error-800;
  }
  
  .status-info {
    @apply status-indicator bg-primary-100 text-primary-800;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600;
  }
  
  .loading-pulse {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Market Brand Colors */
  .market-coto {
    @apply bg-red-500 text-white;
  }
  
  .market-carrefour {
    @apply bg-blue-600 text-white;
  }
  
  .market-jumbo {
    @apply bg-orange-500 text-white;
  }
  
  .market-disco {
    @apply bg-green-600 text-white;
  }
  
  .market-vea {
    @apply bg-purple-600 text-white;
  }

  /* Price Display */
  .price-display {
    @apply text-lg font-semibold text-gray-900;
  }
  
  .price-original {
    @apply text-sm text-gray-500 line-through;
  }
  
  .price-discount {
    @apply text-sm font-medium text-success-600;
  }

  /* Grid Layouts */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@layer utilities {
  /* Custom Scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  /* Focus Utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
}
