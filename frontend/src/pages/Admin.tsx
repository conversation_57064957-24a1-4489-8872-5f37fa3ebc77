import React, { useState, useEffect } from 'react';
import {
  CogIcon,
  ChartBarIcon,
  ServerIcon,
  CircleStackIcon,
  DocumentTextIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { apiClient } from '../services/api';

// Import the new admin components
import ScraperControls from '../components/Admin/ScraperControls';
import ScrapingLogs from '../components/Admin/ScrapingLogs';
import SystemConfiguration from '../components/Admin/SystemConfiguration';
import PerformanceMonitoring from '../components/Admin/PerformanceMonitoring';
import DataManagement from '../components/Admin/DataManagement';

// Import types
import {
  ScraperStatus,
  LogEntry,
  SystemMetrics,
  DatabaseStats,
  MarketName
} from '../types';

const Admin: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real data from API
  const [scraperStatuses, setScraperStatuses] = useState<ScraperStatus[]>([]);
  const [systemStats, setSystemStats] = useState<any>(null);
  const [scrapingStatus, setScrapingStatus] = useState<any>(null);

  const [logs, setLogs] = useState<LogEntry[]>([]);

  // Load real data from API
  useEffect(() => {
    const loadAdminData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load system stats and scraping status in parallel
        const [statsResponse, statusResponse] = await Promise.all([
          apiClient.admin.getSystemStats(),
          apiClient.admin.getScrapingStatus()
        ]);

        setSystemStats(statsResponse);
        setScrapingStatus(statusResponse);

        // Convert scraping status to ScraperStatus format
        const markets = ['coto', 'carrefour', 'jumbo', 'disco', 'vea'];
        const scraperData: ScraperStatus[] = markets.map(market => ({
          market: market as MarketName,
          status: statusResponse.markets[market]?.status || 'idle',
          lastRun: statusResponse.markets[market]?.last_run || null,
          nextRun: undefined, // Not provided by current API
          productsScraped: statsResponse.database?.products_by_market?.[market] || 0,
          errors: 0, // Not provided by current API
          config: {
            markets_enabled: [market as MarketName],
            rate_limit_delay: 2.0,
            max_retries: 3,
            timeout: 30,
            headless: true,
            user_agent_rotation: true,
            max_products_per_session: 50,
            categories_to_scrape: []
          }
        }));

        setScraperStatuses(scraperData);

      } catch (err) {
        console.error('Failed to load admin data:', err);
        setError('Failed to load admin data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadAdminData();
  }, []);

  // Derived metrics from real data
  const systemMetrics: SystemMetrics = {
    productsScrapedLast24h: systemStats?.database?.total_products || 0,
    successRate: systemStats?.scraping?.success_rate || 0,
    averageResponseTime: 1200, // Not available from current API
    cpuUsage: 0, // Not available from current API
    memoryUsage: 0, // Not available from current API
    databaseSize: 'N/A', // Not available from current API
  };

  const databaseStats: DatabaseStats = {
    totalProducts: systemStats?.database?.total_products || 0,
    productsByMarket: systemStats?.database?.products_by_market || {},
    lastUpdated: systemStats?.system?.timestamp || new Date().toISOString(),
    databaseSize: 'N/A', // Not available from current API
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Admin Panel
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage scrapers, monitor performance, and configure system settings
            </p>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading admin data...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading admin data</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Tabs */}
        {!loading && !error && (
        <>
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            {[
              { id: 'overview', name: 'Overview', icon: ChartBarIcon },
              { id: 'scrapers', name: 'Scrapers', icon: CogIcon },
              { id: 'logs', name: 'Logs', icon: DocumentTextIcon },
              { id: 'performance', name: 'Performance', icon: ChartBarIcon },
              { id: 'config', name: 'Configuration', icon: ServerIcon },
              { id: 'data', name: 'Data Management', icon: CircleStackIcon },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="space-y-8">
          {(() => {
            // Real API handlers
          const handleStartScraper = async (market: MarketName) => {
            try {
              await apiClient.admin.startScraper(market);
              setScraperStatuses(prev =>
                prev.map(s => s.market === market ? { ...s, status: 'running' as const } : s)
              );
            } catch (err) {
              console.error(`Failed to start scraper for ${market}:`, err);
            }
          };

          const handleStopScraper = async (market: MarketName) => {
            try {
              await apiClient.admin.stopScraper(market);
              setScraperStatuses(prev =>
                prev.map(s => s.market === market ? { ...s, status: 'stopped' as const } : s)
              );
            } catch (err) {
              console.error(`Failed to stop scraper for ${market}:`, err);
            }
          };

          const handleStartAll = async () => {
            const markets: MarketName[] = ['coto', 'carrefour', 'jumbo', 'disco', 'vea'];
            try {
              await Promise.all(markets.map(market => apiClient.admin.startScraper(market)));
              setScraperStatuses(prev =>
                prev.map(s => ({ ...s, status: 'running' as const }))
              );
            } catch (err) {
              console.error('Failed to start all scrapers:', err);
            }
          };

          const handleStopAll = async () => {
            const markets: MarketName[] = ['coto', 'carrefour', 'jumbo', 'disco', 'vea'];
            try {
              await Promise.all(markets.map(market => apiClient.admin.stopScraper(market)));
              setScraperStatuses(prev =>
                prev.map(s => ({ ...s, status: 'stopped' as const }))
              );
            } catch (err) {
              console.error('Failed to stop all scrapers:', err);
            }
          };

          const handleConfigChange = async (market: MarketName, config: any) => {
            try {
              console.log(`Updating config for ${market}:`, config);
              setScraperStatuses(prev =>
                prev.map(s => s.market === market ? { ...s, config } : s)
              );
            } catch (err) {
              console.error(`Failed to update config for ${market}:`, err);
            }
          };

          const handleExportLogs = () => console.log('Exporting logs - feature coming soon');
          const handleClearLogs = () => { console.log('Clearing logs - feature coming soon'); setLogs([]); };
          const handleRefreshLogs = async () => {
            try {
              const logsResponse = await apiClient.admin.getLogs();
              setLogs(logsResponse || []);
            } catch (err) {
              console.error('Failed to refresh logs:', err);
            }
          };
          const handleSaveConfig = async (config: any) => {
            try {
              await apiClient.admin.updateConfig(config);
              console.log('System configuration saved successfully');
            } catch (err) {
              console.error('Failed to save system configuration:', err);
            }
          };
          const handleExportData = (market?: MarketName) => console.log(`Exporting data${market ? ` for ${market}` : ''} - feature coming soon`);
          const handleImportData = (file: File) => console.log('Importing data from file:', file.name, '- feature coming soon');
          const handleCleanupData = (options: any) => console.log('Cleaning up data with options:', options, '- feature coming soon');
          const handleOptimizeDatabase = () => console.log('Optimizing database - feature coming soon');

          // Return the tab content
          return (
            <>
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Quick Stats Cards */}
              <div className="card">
                <div className="card-body">
                  <div className="flex items-center mb-4">
                    <CogIcon className="h-8 w-8 text-blue-500 mr-3" />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Active Scrapers
                    </h2>
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {scraperStatuses.filter(s => s.status === 'running').length}
                  </div>
                  <p className="text-sm text-gray-600">
                    of {scraperStatuses.length} total scrapers
                  </p>
                </div>
              </div>

              <div className="card">
                <div className="card-body">
                  <div className="flex items-center mb-4">
                    <ChartBarIcon className="h-8 w-8 text-green-500 mr-3" />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Products Today
                    </h2>
                  </div>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {systemMetrics.productsScrapedLast24h.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">
                    {systemMetrics.successRate}% success rate
                  </p>
                </div>
              </div>

              <div className="card">
                <div className="card-body">
                  <div className="flex items-center mb-4">
                    <CircleStackIcon className="h-8 w-8 text-purple-500 mr-3" />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Total Products
                    </h2>
                  </div>
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {databaseStats.totalProducts.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">
                    {databaseStats.databaseSize} database size
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'scrapers' && (
            <ScraperControls
              scrapers={scraperStatuses}
              onStartScraper={async (market: MarketName) => {
                try {
                  console.log(`Starting scraper for ${market}`);
                  setScraperStatuses(prev =>
                    prev.map(s => s.market === market ? { ...s, status: 'running' as const } : s)
                  );

                  // Call the actual API endpoint
                  const response = await apiClient.scraper.startScraping({
                    markets_enabled: [market],
                    rate_limit_delay: 2.0,
                    max_retries: 3,
                    timeout: 30,
                    headless: true,
                    user_agent_rotation: true,
                    max_products_per_session: 50
                  });

                  console.log('Scraping started:', response);
                } catch (error) {
                  console.error(`Failed to start scraper for ${market}:`, error);
                  setScraperStatuses(prev =>
                    prev.map(s => s.market === market ? { ...s, status: 'idle' as const } : s)
                  );
                }
              }}
              onStopScraper={async (market: MarketName) => {
                try {
                  console.log(`Stopping scraper for ${market}`);
                  setScraperStatuses(prev =>
                    prev.map(s => s.market === market ? { ...s, status: 'stopped' as const } : s)
                  );

                  // Note: We would need the session ID to stop a specific scraper
                  // For now, just update the UI
                } catch (error) {
                  console.error(`Failed to stop scraper for ${market}:`, error);
                }
              }}
              onStartAll={async () => {
                try {
                  console.log('Starting all scrapers');
                  setScraperStatuses(prev =>
                    prev.map(s => ({ ...s, status: 'running' as const }))
                  );

                  // Call the actual API endpoint for all markets
                  const response = await apiClient.scraper.startScraping({
                    markets_enabled: ['coto', 'carrefour', 'jumbo', 'disco', 'vea'] as MarketName[],
                    rate_limit_delay: 2.0,
                    max_retries: 3,
                    timeout: 30,
                    headless: true,
                    user_agent_rotation: true,
                    max_products_per_session: 50
                  });

                  console.log('All scrapers started:', response);
                } catch (error) {
                  console.error('Failed to start all scrapers:', error);
                  setScraperStatuses(prev =>
                    prev.map(s => ({ ...s, status: 'idle' as const }))
                  );
                }
              }}
              onStopAll={() => {
                console.log('Stopping all scrapers');
                setScraperStatuses(prev =>
                  prev.map(s => ({ ...s, status: 'stopped' as const }))
                );
              }}
              onConfigChange={(market: MarketName, config: any) => {
                console.log(`Updating config for ${market}:`, config);
                setScraperStatuses(prev =>
                  prev.map(s => s.market === market ? { ...s, config } : s)
                );
              }}
            />
          )}

          {activeTab === 'logs' && (
            <ScrapingLogs
              logs={logs}
              onExportLogs={() => console.log('Exporting logs')}
              onClearLogs={() => { console.log('Clearing logs'); setLogs([]); }}
              onRefresh={() => console.log('Refreshing logs')}
            />
          )}

          {activeTab === 'performance' && (
            <PerformanceMonitoring metrics={systemMetrics} />
          )}

          {activeTab === 'config' && (
            <SystemConfiguration onSaveConfig={(config: any) => console.log('Saving system configuration:', config)} />
          )}

          {activeTab === 'data' && (
            <DataManagement
              stats={databaseStats}
              onExportData={(market?: MarketName) => console.log(`Exporting data${market ? ` for ${market}` : ''}`)}
              onImportData={(file: File) => console.log('Importing data from file:', file.name)}
              onCleanupData={(options: any) => console.log('Cleaning up data with options:', options)}
              onOptimizeDatabase={() => console.log('Optimizing database')}
            />
          )}
            </>
          );
        })()}
        </div>
        </>
        )}
      </div>
    </Layout>
  );
};

export default Admin;
