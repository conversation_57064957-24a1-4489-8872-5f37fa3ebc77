import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  MagnifyingGlassIcon,
  ChartBarIcon,
  XMarkIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import PriceChart from '../components/Compare/PriceChart';
import ComparisonTable from '../components/Compare/ComparisonTable';
import { PriceComparison } from '../types';
import { apiClient } from '../services/api';

const Compare: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get('product') || '');
  const [comparison, setComparison] = useState<PriceComparison | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const productParam = searchParams.get('product');
    if (productParam) {
      setSearchQuery(productParam);
      performComparison(productParam);
    }
  }, [searchParams]);

  const performComparison = async (productName: string) => {
    if (!productName.trim()) return;

    try {
      setLoading(true);
      setError(null);
      
      const comparisonData = await apiClient.priceComparison.compareProduct(productName);
      setComparison(comparisonData);
      
      // Update URL
      const newParams = new URLSearchParams();
      newParams.set('product', productName);
      setSearchParams(newParams);
    } catch (err: any) {
      setError(err.message || 'Failed to compare prices');
      setComparison(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performComparison(searchQuery);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setComparison(null);
    setError(null);
    setSearchParams({});
  };

  const getSavingsInfo = () => {
    if (!comparison || comparison.prices.length < 2) return null;

    const availablePrices = comparison.prices.filter(p => p.availability);
    if (availablePrices.length < 2) return null;

    const maxPrice = Math.max(...availablePrices.map(p => p.price));
    const minPrice = Math.min(...availablePrices.map(p => p.price));
    const savings = maxPrice - minPrice;
    const savingsPercentage = ((savings / maxPrice) * 100);

    return {
      maxPrice,
      minPrice,
      savings,
      savingsPercentage,
    };
  };

  const savingsInfo = getSavingsInfo();

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Price Comparison
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Compare prices for the same product across different supermarkets
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="card">
          <div className="card-body">
            <form onSubmit={handleSearch} className="flex space-x-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Enter product name to compare prices..."
                  className="form-input pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={clearSearch}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>
              <button type="submit" className="btn btn-primary" disabled={loading}>
                {loading ? (
                  <div className="loading-spinner"></div>
                ) : (
                  <>
                    <ChartBarIcon className="h-4 w-4 mr-2" />
                    Compare
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="card">
            <div className="card-body text-center py-12">
              <div className="loading-spinner mx-auto mb-4"></div>
              <p className="text-gray-500">Comparing prices across markets...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="card border-error-200 bg-error-50">
            <div className="card-body">
              <div className="flex items-center space-x-2 text-error-600">
                <XMarkIcon className="h-5 w-5" />
                <span className="font-medium">Error</span>
              </div>
              <p className="mt-2 text-sm text-error-600">{error}</p>
            </div>
          </div>
        )}

        {/* Comparison Results */}
        {comparison && !loading && (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="card">
                <div className="card-body text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {comparison.prices.filter(p => p.availability).length}
                  </div>
                  <div className="text-sm text-gray-500">Markets Available</div>
                </div>
              </div>
              
              <div className="card">
                <div className="card-body text-center">
                  <div className="text-2xl font-bold text-success-600">
                    ${comparison.best_price.price.toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-500">Best Price</div>
                  <div className="text-xs text-gray-400 mt-1 uppercase">
                    {comparison.best_price.market}
                  </div>
                </div>
              </div>
              
              <div className="card">
                <div className="card-body text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    ${comparison.average_price.toFixed(2)}
                  </div>
                  <div className="text-sm text-gray-500">Average Price</div>
                </div>
              </div>
              
              {savingsInfo && (
                <div className="card">
                  <div className="card-body text-center">
                    <div className="text-2xl font-bold text-warning-600">
                      ${savingsInfo.savings.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-500">Max Savings</div>
                    <div className="text-xs text-gray-400 mt-1">
                      {savingsInfo.savingsPercentage.toFixed(1)}% difference
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {comparison.product_name}
                </h3>
              </div>
              <div className="card-body">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Price Range:</span>
                    <span className="ml-2 font-medium">
                      ${comparison.price_range.min.toFixed(2)} - ${comparison.price_range.max.toFixed(2)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Best Deal:</span>
                    <span className="ml-2 font-medium text-success-600 uppercase">
                      {comparison.best_price.market}
                    </span>
                  </div>
                  {savingsInfo && (
                    <div>
                      <span className="text-gray-500">Potential Savings:</span>
                      <span className="ml-2 font-medium text-warning-600">
                        ${savingsInfo.savings.toFixed(2)} ({savingsInfo.savingsPercentage.toFixed(1)}%)
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Price Chart */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">Price Comparison Chart</h3>
              </div>
              <div className="card-body">
                <PriceChart comparison={comparison} height={400} />
              </div>
            </div>

            {/* Detailed Comparison Table */}
            <ComparisonTable comparison={comparison} />

            {/* Tips */}
            <div className="card border-primary-200 bg-primary-50">
              <div className="card-body">
                <div className="flex items-start space-x-2">
                  <InformationCircleIcon className="h-5 w-5 text-primary-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-primary-900">Shopping Tips</h4>
                    <ul className="mt-2 text-sm text-primary-800 space-y-1">
                      <li>• Prices shown include current promotions and discounts</li>
                      <li>• Check availability before visiting the store</li>
                      <li>• Consider delivery fees when comparing online prices</li>
                      <li>• Prices are updated regularly but may change</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!comparison && !loading && !error && (
          <div className="card">
            <div className="card-body text-center py-12">
              <div className="text-gray-400 mb-4">
                <ChartBarIcon className="mx-auto h-12 w-12" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Compare Product Prices</h3>
              <p className="text-gray-500 mb-6">
                Enter a product name above to see price comparisons across all supermarkets.
              </p>
              <div className="text-sm text-gray-400">
                <p>Try searching for popular items like:</p>
                <div className="flex flex-wrap justify-center gap-2 mt-2">
                  {['Coca Cola', 'Leche La Serenísima', 'Pan Lactal', 'Aceite Natura'].map((example) => (
                    <button
                      key={example}
                      onClick={() => {
                        setSearchQuery(example);
                        performComparison(example);
                      }}
                      className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
                    >
                      {example}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Compare;
