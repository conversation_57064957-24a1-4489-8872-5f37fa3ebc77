import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import {
  BuildingStorefrontIcon,
  ChartBarIcon,
  ClockIcon,
  ArrowRightIcon,
  TagIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { Market, Product, SearchResponse } from '../types';
import { apiClient } from '../services/api';
import { formatDistanceToNow } from 'date-fns';
import ProductCard from '../components/Search/ProductCard';

const Markets: React.FC = () => {
  const { marketName } = useParams<{ marketName?: string }>();
  const [markets, setMarkets] = useState<Market[]>([]);
  const [marketProducts, setMarketProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // If marketName is provided, we're showing individual market page
  const isIndividualMarket = !!marketName;

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (isIndividualMarket && marketName) {
          // Fetch products for specific market
          const response: SearchResponse = await apiClient.products.getProducts({
            market: [marketName],
            page: 1,
            limit: 50, // Show more products on market page
          });
          setMarketProducts(response.products);

          // Also fetch markets data for market info
          const marketsData = await apiClient.markets.getMarkets();
          setMarkets(marketsData);
        } else {
          // Fetch all markets for overview page
          const marketsData = await apiClient.markets.getMarkets();
          setMarkets(marketsData);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isIndividualMarket, marketName]);

  const getMarketColor = (marketName: string) => {
    const colors: Record<string, string> = {
      coto: 'from-red-500 to-red-600',
      carrefour: 'from-blue-500 to-blue-600',
      jumbo: 'from-orange-500 to-orange-600',
      disco: 'from-green-500 to-green-600',
      vea: 'from-purple-500 to-purple-600',
    };
    return colors[marketName.toLowerCase()] || 'from-gray-500 to-gray-600';
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return 'text-success-600 bg-success-50 border-success-200';
      case 'scraping':
        return 'text-warning-600 bg-warning-50 border-warning-200';
      case 'inactive':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="card animate-pulse">
                <div className="h-32 bg-gray-200 rounded-t-lg"></div>
                <div className="card-body space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="card">
          <div className="card-body text-center py-12">
            <div className="text-error-600 mb-4">
              <BuildingStorefrontIcon className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Markets</h3>
            <p className="text-gray-500">{error}</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Individual market page
  if (isIndividualMarket && marketName) {
    const currentMarket = markets.find(m => m.name === marketName);

    return (
      <Layout>
        <div className="space-y-6">
          {/* Back Navigation */}
          <div className="flex items-center space-x-4">
            <Link
              to="/markets"
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Markets
            </Link>
          </div>

          {/* Market Header */}
          {currentMarket && (
            <div className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${getMarketColor(currentMarket.name)} flex items-center justify-center`}>
                      <BuildingStorefrontIcon className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900">
                        {currentMarket.display_name || currentMarket.name}
                      </h1>
                      <p className="text-gray-600">
                        {currentMarket.product_count} products • Avg. ${currentMarket.average_price.toFixed(2)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(currentMarket.status)}`}>
                      {currentMarket.status || 'Active'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Products Grid */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                Products ({marketProducts.length})
              </h2>
              <Link
                to={`/search?market=${marketName}`}
                className="btn btn-outline btn-sm"
              >
                View All in Search
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>

            {marketProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {marketProducts.map((product) => (
                  <ProductCard
                    key={product._id}
                    product={product}
                    onCompare={() => {}} // Individual market page doesn't need compare
                  />
                ))}
              </div>
            ) : (
              <div className="card">
                <div className="card-body text-center py-12">
                  <TagIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Found</h3>
                  <p className="text-gray-500">
                    No products available for this market at the moment.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </Layout>
    );
  }

  // Markets overview page
  const totalProducts = markets.reduce((sum, market) => sum + market.product_count, 0);
  const averagePrice = markets.reduce((sum, market) => sum + market.average_price, 0) / markets.length;

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Supermarkets
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Browse products and compare prices across Argentina's major supermarket chains
            </p>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <div className="card-body text-center">
              <div className="text-2xl font-bold text-gray-900">{markets.length}</div>
              <div className="text-sm text-gray-500">Active Markets</div>
            </div>
          </div>
          <div className="card">
            <div className="card-body text-center">
              <div className="text-2xl font-bold text-primary-600">
                {totalProducts.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">Total Products</div>
            </div>
          </div>
          <div className="card">
            <div className="card-body text-center">
              <div className="text-2xl font-bold text-success-600">
                ${averagePrice.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Average Price</div>
            </div>
          </div>
        </div>

        {/* Markets Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {markets.map((market) => (
            <div key={market.name} className="card overflow-hidden hover:shadow-lg transition-shadow duration-200">
              {/* Market Header with Gradient */}
              <div className={`h-32 bg-gradient-to-br ${getMarketColor(market.name)} relative`}>
                <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                <div className="relative h-full flex items-center justify-center">
                  <div className="text-center text-white">
                    <BuildingStorefrontIcon className="h-12 w-12 mx-auto mb-2" />
                    <h3 className="text-xl font-bold">{market.display_name}</h3>
                  </div>
                </div>
                
                {/* Status Badge */}
                {market.status && (
                  <div className="absolute top-4 right-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(market.status)}`}>
                      {market.status}
                    </span>
                  </div>
                )}
              </div>

              {/* Market Content */}
              <div className="card-body space-y-4">
                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {market.product_count.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">Products</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      ${market.average_price.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-500">Avg. Price</div>
                  </div>
                </div>

                {/* Last Scraped */}
                {market.last_scraped && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <ClockIcon className="h-4 w-4" />
                    <span>
                      Updated {formatDistanceToNow(new Date(market.last_scraped), { addSuffix: true })}
                    </span>
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2">
                  <Link
                    to={`/markets/${market.name}`}
                    className="flex-1 btn btn-outline btn-sm"
                  >
                    <TagIcon className="h-4 w-4 mr-1" />
                    View Products
                  </Link>
                  <Link
                    to={`/search?market=${market.name}`}
                    className="flex-1 btn btn-primary btn-sm"
                  >
                    <ChartBarIcon className="h-4 w-4 mr-1" />
                    Browse
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {markets.length === 0 && (
          <div className="card">
            <div className="card-body text-center py-12">
              <BuildingStorefrontIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No markets found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Markets will appear here once the scraper starts collecting data.
              </p>
              <div className="mt-6">
                <Link to="/admin" className="btn btn-primary">
                  <ArrowRightIcon className="h-4 w-4 mr-2" />
                  Start Scraping
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Market Comparison */}
        {markets.length > 1 && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">Market Comparison</h3>
            </div>
            <div className="card-body">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Market
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Products
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg. Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Updated
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {markets
                      .sort((a, b) => b.product_count - a.product_count)
                      .map((market) => (
                        <tr key={market.name} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-3">
                              <div className={`h-3 w-3 rounded-full bg-gradient-to-r ${getMarketColor(market.name)}`}></div>
                              <Link
                                to={`/markets/${market.name}`}
                                className="text-sm font-medium text-gray-900 hover:text-primary-600"
                              >
                                {market.display_name}
                              </Link>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {market.product_count.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${market.average_price.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {market.status && (
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(market.status)}`}>
                                {market.status}
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {market.last_scraped 
                              ? formatDistanceToNow(new Date(market.last_scraped), { addSuffix: true })
                              : 'Never'
                            }
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Markets;
