import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import SearchFilters from '../components/Search/SearchFilters';
import SearchResults from '../components/Search/SearchResults';
import { SearchParams, SearchResponse, Product, ViewMode } from '../types';
import { apiClient } from '../services/api';

const Search: React.FC = () => {
  const [urlSearchParams, setUrlSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(urlSearchParams.get('q') || '');
  const [filters, setFilters] = useState<SearchParams>({
    query: urlSearchParams.get('q') || '',
    page: parseInt(urlSearchParams.get('page') || '1'),
    limit: parseInt(urlSearchParams.get('limit') || '12'),
    market: urlSearchParams.getAll('market').length > 0 ? urlSearchParams.getAll('market') : undefined,
    category: urlSearchParams.getAll('category').length > 0 ? urlSearchParams.getAll('category') : undefined,
    min_price: urlSearchParams.get('min_price') ? parseFloat(urlSearchParams.get('min_price')!) : undefined,
    max_price: urlSearchParams.get('max_price') ? parseFloat(urlSearchParams.get('max_price')!) : undefined,
    availability: urlSearchParams.get('availability') === 'true' ? true : undefined,
    on_promotion: urlSearchParams.get('on_promotion') === 'true' ? true : undefined,
    sort_by: (urlSearchParams.get('sort_by') as 'price' | 'name' | 'discount' | 'updated_at') || undefined,
    sort_order: (urlSearchParams.get('sort_order') as 'asc' | 'desc') || undefined,
  });
  const [results, setResults] = useState<SearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [compareList, setCompareList] = useState<Product[]>([]);

  // Debounced search function
  const performSearch = useCallback(async (searchParams: SearchParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.products.getProducts(searchParams);
      setResults(response);
      
      // Update URL params
      const newUrlParams = new URLSearchParams();
      if (searchParams.query) newUrlParams.set('q', searchParams.query);
      if (searchParams.page && searchParams.page > 1) newUrlParams.set('page', searchParams.page.toString());
      if (searchParams.limit && searchParams.limit !== 12) newUrlParams.set('limit', searchParams.limit.toString());
      if (searchParams.market?.length) searchParams.market.forEach(m => newUrlParams.append('market', m));
      if (searchParams.category?.length) searchParams.category.forEach(c => newUrlParams.append('category', c));
      if (searchParams.min_price) newUrlParams.set('min_price', searchParams.min_price.toString());
      if (searchParams.max_price) newUrlParams.set('max_price', searchParams.max_price.toString());
      if (searchParams.availability) newUrlParams.set('availability', 'true');
      if (searchParams.on_promotion) newUrlParams.set('on_promotion', 'true');
      if (searchParams.sort_by) newUrlParams.set('sort_by', searchParams.sort_by);
      if (searchParams.sort_order) newUrlParams.set('sort_order', searchParams.sort_order);
      
      setUrlSearchParams(newUrlParams);
    } catch (err: any) {
      setError(err.message || 'Failed to search products');
      setResults(null);
    } finally {
      setLoading(false);
    }
  }, [setUrlSearchParams]);

  // Effect for initial search and filter changes
  useEffect(() => {
    if (filters.query || Object.keys(filters).some(key => 
      key !== 'query' && key !== 'page' && key !== 'limit' && filters[key as keyof SearchParams]
    )) {
      performSearch(filters);
    }
  }, [filters, performSearch]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({
      ...prev,
      query: searchQuery,
      page: 1, // Reset to first page on new search
    }));
  };

  const handleFiltersChange = (newFilters: SearchParams) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handleClearFilters = () => {
    const clearedFilters: SearchParams = {
      query: filters.query, // Keep the search query
      page: 1,
      limit: filters.limit,
    };
    setFilters(clearedFilters);
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleCompare = (product: Product) => {
    setCompareList(prev => {
      const exists = prev.find(p => p._id === product._id);
      if (exists) {
        return prev.filter(p => p._id !== product._id);
      } else if (prev.length < 5) { // Limit to 5 products for comparison
        return [...prev, product];
      } else {
        // Replace the first product if at limit
        return [product, ...prev.slice(1)];
      }
    });
  };

  const removeFromCompare = (productId: string) => {
    setCompareList(prev => prev.filter(p => p._id !== productId));
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Product Search
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Search and compare products across all supermarkets
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="card">
          <div className="card-body">
            <form onSubmit={handleSearch} className="flex space-x-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search for products..."
                  className="form-input pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearchQuery('');
                      setFilters(prev => ({ ...prev, query: '', page: 1 }));
                    }}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>
              <button type="submit" className="btn btn-primary">
                Search
              </button>
            </form>
          </div>
        </div>

        {/* Compare Bar */}
        {compareList.length > 0 && (
          <div className="card border-primary-200 bg-primary-50">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-sm font-medium text-primary-900">
                    Compare Products ({compareList.length}/5)
                  </h3>
                  <div className="flex space-x-2">
                    {compareList.map((product) => (
                      <div
                        key={product._id}
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                      >
                        <span className="truncate max-w-24">{product.name}</span>
                        <button
                          onClick={() => removeFromCompare(product._id)}
                          className="ml-1 text-primary-600 hover:text-primary-800"
                        >
                          <XMarkIcon className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCompareList([])}
                    className="text-sm text-primary-600 hover:text-primary-800"
                  >
                    Clear All
                  </button>
                  <button className="btn btn-primary btn-sm">
                    Compare Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <SearchFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={handleClearFilters}
            />
          </div>

          {/* Search Results */}
          <div className="lg:col-span-3">
            <SearchResults
              results={results}
              loading={loading}
              error={error}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
              onPageChange={handlePageChange}
              onCompare={handleCompare}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Search;
