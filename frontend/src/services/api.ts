import axios, { AxiosResponse, AxiosError } from 'axios';
import {
  HealthStatus,
  Market,
  Product,
  SearchParams,
  SearchResponse,
  Category,
  PriceComparison,
  MarketStats,
  ScrapingSession,
  ScrapingConfig,
  ApiError
} from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    const apiError: ApiError = {
      message: 'An error occurred',
      status: error.response?.status,
      details: error.response?.data,
    };

    if (error.response?.status === 404) {
      apiError.message = 'Resource not found';
    } else if (error.response?.status === 500) {
      apiError.message = 'Internal server error';
    } else if (error.code === 'ECONNABORTED') {
      apiError.message = 'Request timeout';
    } else if (error.message === 'Network Error') {
      apiError.message = 'Network connection error';
    } else if (error.response?.data && typeof error.response.data === 'object' && 'detail' in error.response.data) {
      apiError.message = (error.response.data as any).detail;
    }

    console.error('API Error:', apiError);
    return Promise.reject(apiError);
  }
);

// Health and System APIs
export const healthApi = {
  getHealth: (): Promise<HealthStatus> =>
    api.get('/api/v1/health/').then(res => res.data),

  getDetailedHealth: (): Promise<HealthStatus> =>
    api.get('/api/v1/health/detailed/').then(res => res.data),
};

// Market APIs
export const marketApi = {
  getMarkets: (): Promise<Market[]> =>
    api.get('/api/v1/markets/').then(res => res.data),

  getMarket: (marketName: string): Promise<MarketStats> =>
    api.get(`/api/v1/markets/${marketName}/`).then(res => res.data),

  getMarketStats: (marketName: string): Promise<MarketStats> =>
    api.get(`/api/v1/markets/${marketName}/stats/`).then(res => res.data),
};

// Product APIs
export const productApi = {
  getProducts: (params?: SearchParams): Promise<SearchResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params?.query) queryParams.append('q', params.query);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params?.sort_order) queryParams.append('sort_order', params.sort_order);
    if (params?.market) params.market.forEach(m => queryParams.append('market', m));
    if (params?.category) params.category.forEach(c => queryParams.append('category', c));
    if (params?.min_price) queryParams.append('min_price', params.min_price.toString());
    if (params?.max_price) queryParams.append('max_price', params.max_price.toString());
    if (params?.availability !== undefined) queryParams.append('availability', params.availability.toString());
    if (params?.on_promotion !== undefined) queryParams.append('on_promotion', params.on_promotion.toString());
    if (params?.brand) params.brand.forEach(b => queryParams.append('brand', b));

    return api.get(`/api/v1/products/?${queryParams.toString()}`).then(res => res.data);
  },

  getProduct: (productId: string): Promise<Product> =>
    api.get(`/api/v1/products/${productId}/`).then(res => res.data),



  searchProducts: (query: string, filters?: SearchParams): Promise<SearchResponse> =>
    productApi.getProducts({ ...filters, query }),

  getProductsByMarket: (market: string, params?: SearchParams): Promise<SearchResponse> =>
    productApi.getProducts({ ...params, market: [market] }),

  getProductsByCategory: (category: string, params?: SearchParams): Promise<SearchResponse> =>
    productApi.getProducts({ ...params, category: [category] }),
};

// Category APIs
export const categoryApi = {
  getCategories: (): Promise<Category[]> =>
    api.get('/api/v1/products/categories').then(res => res.data),

  getCategoriesByMarket: (market: string): Promise<Category[]> =>
    api.get(`/api/v1/products/categories?market=${market}`).then(res => res.data),
};

// Price Comparison APIs
export const priceComparisonApi = {
  compareProduct: (productName: string): Promise<PriceComparison> =>
    api.get(`/api/v1/products/price-comparison?product_name=${encodeURIComponent(productName)}`).then(res => res.data),

  compareSimilarProducts: (productId: string): Promise<PriceComparison[]> =>
    api.get(`/api/v1/products/price-comparison?product_name=${encodeURIComponent(productId)}`).then(res => res.data),
};

// Scraper Control APIs (Admin)
export const scraperApi = {
  getSessions: (): Promise<ScrapingSession[]> =>
    api.get('/api/v1/admin/scraping/sessions').then(res => res.data),
  
  getSession: (sessionId: string): Promise<ScrapingSession> =>
    api.get(`/api/v1/admin/scraping/sessions/${sessionId}`).then(res => res.data),
  
  startScraping: (config: ScrapingConfig): Promise<ScrapingSession> =>
    api.post('/api/v1/admin/scraping/start', config).then(res => res.data),
  
  stopScraping: (sessionId: string): Promise<{ message: string }> =>
    api.post(`/api/v1/admin/scraping/stop/${sessionId}`).then(res => res.data),
  
  getScrapingLogs: (sessionId?: string): Promise<string[]> => {
    const url = sessionId 
      ? `/api/v1/admin/scraping/logs/${sessionId}`
      : '/api/v1/admin/scraping/logs';
    return api.get(url).then(res => res.data);
  },
};

// Statistics APIs
export const statsApi = {
  getOverallStats: (): Promise<{
    total_products: number;
    total_markets: number;
    total_categories: number;
    last_updated: string;
    products_by_market: Record<string, number>;
    products_by_category: Record<string, number>;
  }> =>
    api.get('/api/v1/stats').then(res => res.data),
  
  getPriceDistribution: (): Promise<Array<{ range: string; count: number }>> =>
    api.get('/api/v1/stats/price-distribution').then(res => res.data),
  
  getTopProducts: (limit: number = 10): Promise<Product[]> =>
    api.get(`/api/v1/stats/top-products?limit=${limit}`).then(res => res.data),
  
  getRecentProducts: (limit: number = 10): Promise<Product[]> =>
    api.get(`/api/v1/stats/recent-products?limit=${limit}`).then(res => res.data),
};

// Admin API endpoints
export const adminApi = {
  // System stats
  getSystemStats: (): Promise<any> =>
    api.get('/api/v1/admin/system/stats').then(res => res.data),

  // Scraper status and controls
  getScrapingStatus: (): Promise<any> =>
    api.get('/api/v1/admin/scraping/status').then(res => res.data),

  startScraper: (market: string): Promise<any> =>
    api.post('/api/v1/admin/scraping/start', { markets_enabled: [market] }).then(res => res.data),

  stopScraper: (sessionId: string): Promise<any> =>
    api.post(`/api/v1/admin/scraping/stop/${sessionId}`).then(res => res.data),

  // Logs (using scraping sessions as logs for now)
  getLogs: (params?: { market?: string; level?: string; limit?: number }): Promise<any> =>
    api.get('/api/v1/admin/scraping/sessions', { params: { limit: params?.limit || 50 } }).then(res => res.data),

  // Configuration (not available in current API - return empty config)
  getConfig: (): Promise<any> =>
    Promise.resolve({}),

  updateConfig: (config: any): Promise<any> =>
    Promise.resolve({ message: 'Configuration update not implemented yet', config }),

  // Data management
  cleanupOldData: (params?: { days_old?: number; market?: string }): Promise<any> =>
    api.delete('/api/v1/admin/data/cleanup', { params }).then(res => res.data),

  clearAllData: (params?: { confirm?: boolean; market?: string }): Promise<any> =>
    api.delete('/api/v1/admin/data/clear-all', { params }).then(res => res.data),
};

// Export all APIs as a single object
export const apiClient = {
  health: healthApi,
  markets: marketApi,
  products: productApi,
  categories: categoryApi,
  priceComparison: priceComparisonApi,
  scraper: scraperApi,
  stats: statsApi,
  admin: adminApi,
};

export default apiClient;
