<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Scrap - Network Access Information</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .service {
            background: #ecf0f1;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .service h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .url {
            background: #34495e;
            color: white;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .running { background-color: #27ae60; }
        .test-button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .note {
            background: #f39c12;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Super Scrap - Network Access Information</h1>
        
        <div class="note">
            <strong>📍 Server IP:</strong> **************<br>
            <strong>🌐 Network:</strong> Local Network Access Enabled
        </div>

        <div class="service">
            <h3>🎨 Frontend (Admin Panel)</h3>
            <p><span class="status running">✅ RUNNING</span></p>
            <div class="url">http://**************:3500</div>
            <p>Complete admin interface with dashboard, scraping controls, and product management.</p>
            <button class="test-button" onclick="window.open('http://**************:3500', '_blank')">Open Frontend</button>
        </div>

        <div class="service">
            <h3>🔧 API Backend</h3>
            <p><span class="status running">✅ RUNNING</span></p>
            <div class="url">http://**************:8500</div>
            <p>REST API for direct integration and API documentation.</p>
            <button class="test-button" onclick="window.open('http://**************:8500/docs', '_blank')">API Docs</button>
            <button class="test-button" onclick="testAPI()">Test API</button>
        </div>

        <div class="service">
            <h3>📊 System Stats</h3>
            <div class="url">http://**************:8500/api/v1/admin/system/stats</div>
            <p>Real-time system statistics and health information.</p>
            <button class="test-button" onclick="testStats()">Test Stats</button>
        </div>

        <div class="service">
            <h3>🗄️ Database</h3>
            <p><span class="status running">✅ RUNNING</span></p>
            <div class="url">mongodb://**************:27017</div>
            <p>MongoDB database (internal access only).</p>
        </div>

        <h2>🔍 Troubleshooting</h2>
        <div style="background: #ecf0f1; padding: 15px; border-radius: 5px;">
            <p><strong>If you're getting connection errors:</strong></p>
            <ul>
                <li>✅ Services are running and accessible from the server</li>
                <li>🔥 Check if firewall is blocking ports 3500 and 8500</li>
                <li>🌐 Ensure you're on the same network (192.168.10.x)</li>
                <li>🔄 Try refreshing the page or clearing browser cache</li>
                <li>📱 Test from different devices on the network</li>
            </ul>
        </div>

        <div id="test-results" style="margin-top: 20px;"></div>
    </div>

    <script>
        function testAPI() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 Testing API connection...</p>';
            
            fetch('http://**************:8500/api/v1/health')
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '<div style="background: #27ae60; color: white; padding: 10px; border-radius: 5px;">✅ API Test Successful: ' + JSON.stringify(data) + '</div>';
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<div style="background: #e74c3c; color: white; padding: 10px; border-radius: 5px;">❌ API Test Failed: ' + error.message + '</div>';
                });
        }

        function testStats() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 Testing system stats...</p>';
            
            fetch('http://**************:8500/api/v1/admin/system/stats')
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '<div style="background: #27ae60; color: white; padding: 10px; border-radius: 5px;">✅ Stats Test Successful: ' + JSON.stringify(data, null, 2) + '</div>';
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<div style="background: #e74c3c; color: white; padding: 10px; border-radius: 5px;">❌ Stats Test Failed: ' + error.message + '</div>';
                });
        }

        // Auto-test on page load
        window.onload = function() {
            setTimeout(testAPI, 1000);
        };
    </script>
</body>
</html>
