# Web Scraping
selenium==4.15.2
beautifulsoup4==4.12.2
requests==2.31.0
lxml==4.9.3
fake-useragent==1.4.0

# Database
pymongo==4.6.0
motor==3.3.2

# API Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# Task Scheduling
celery==5.3.4
redis==5.0.1
croniter==2.0.1

# Data Processing
pandas==2.1.3
numpy==1.25.2

# Configuration
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Logging
loguru==0.7.2

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Browser Automation
webdriver-manager==4.0.1
undetected-chromedriver==3.5.4

# Data Validation
validators==0.22.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
tenacity==8.2.3

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Monitoring
prometheus-client==0.19.0
