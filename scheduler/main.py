"""
Scheduler for running scraping tasks at regular intervals.
"""

import os
import asyncio
import logging
from datetime import datetime
from typing import Optional

import redis
from celery import Celery
from croniter import croniter
from dotenv import load_dotenv

from data.models import ScrapingConfig, MarketEnum
from scrapers.scraper_manager import run_scheduled_scraping

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Redis connection
redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
redis_client = redis.from_url(redis_url)

# Celery app
celery_app = Celery(
    'supermarket_scraper',
    broker=redis_url,
    backend=redis_url
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='America/Argentina/Buenos_Aires',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=3600,  # 1 hour timeout
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=10
)


@celery_app.task(bind=True)
def scrape_all_markets_task(self, categories: Optional[list] = None):
    """Celery task to scrape all markets."""
    try:
        logger.info(f"Starting scraping task {self.request.id}")
        
        # Create configuration
        config = ScrapingConfig(
            rate_limit_delay=float(os.getenv("RATE_LIMIT_DELAY", 2.0)),
            max_retries=int(os.getenv("MAX_RETRIES", 3)),
            timeout=int(os.getenv("TIMEOUT", 30)),
            headless=os.getenv("HEADLESS_BROWSER", "true").lower() == "true",
            user_agent_rotation=os.getenv("USER_AGENT_ROTATION", "true").lower() == "true",
            markets_enabled=[
                market for market in MarketEnum 
                if os.getenv(f"{market.value.upper()}_ENABLED", "true").lower() == "true"
            ]
        )
        
        # Run scraping
        results = asyncio.run(run_scheduled_scraping(config, categories))
        
        logger.info(f"Scraping task {self.request.id} completed successfully")
        return {
            "status": "success",
            "results": results,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Scraping task {self.request.id} failed: {e}")
        raise


@celery_app.task(bind=True)
def scrape_single_market_task(self, market: str, categories: Optional[list] = None):
    """Celery task to scrape a single market."""
    try:
        logger.info(f"Starting single market scraping task {self.request.id} for {market}")
        
        # Validate market
        try:
            market_enum = MarketEnum(market)
        except ValueError:
            raise ValueError(f"Invalid market: {market}")
        
        # Create configuration
        config = ScrapingConfig(
            rate_limit_delay=float(os.getenv("RATE_LIMIT_DELAY", 2.0)),
            max_retries=int(os.getenv("MAX_RETRIES", 3)),
            timeout=int(os.getenv("TIMEOUT", 30)),
            headless=os.getenv("HEADLESS_BROWSER", "true").lower() == "true",
            user_agent_rotation=os.getenv("USER_AGENT_ROTATION", "true").lower() == "true",
            markets_enabled=[market_enum]
        )
        
        # Run scraping
        results = asyncio.run(run_scheduled_scraping(config, categories))
        
        logger.info(f"Single market scraping task {self.request.id} completed successfully")
        return {
            "status": "success",
            "market": market,
            "results": results,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Single market scraping task {self.request.id} failed: {e}")
        raise


class ScrapingScheduler:
    """Scheduler for managing scraping tasks."""
    
    def __init__(self):
        self.schedule = os.getenv("SCRAPING_SCHEDULE", "0 */6 * * *")  # Every 6 hours
        self.running = False
    
    async def start(self):
        """Start the scheduler."""
        logger.info("Starting scraping scheduler")
        self.running = True
        
        # Schedule the next run
        await self._schedule_next_run()
        
        # Keep the scheduler running
        while self.running:
            await asyncio.sleep(60)  # Check every minute
    
    async def stop(self):
        """Stop the scheduler."""
        logger.info("Stopping scraping scheduler")
        self.running = False
    
    async def _schedule_next_run(self):
        """Schedule the next scraping run."""
        try:
            cron = croniter(self.schedule, datetime.utcnow())
            next_run = cron.get_next(datetime)
            
            logger.info(f"Next scraping run scheduled for: {next_run}")
            
            # Calculate delay until next run
            delay = (next_run - datetime.utcnow()).total_seconds()
            
            # Schedule the task
            await asyncio.sleep(delay)
            
            # Execute scraping
            logger.info("Executing scheduled scraping")
            task = scrape_all_markets_task.delay()
            logger.info(f"Scheduled scraping task started with ID: {task.id}")
            
            # Schedule the next run
            if self.running:
                await self._schedule_next_run()
                
        except Exception as e:
            logger.error(f"Error scheduling next run: {e}")
            # Retry in 5 minutes
            await asyncio.sleep(300)
            if self.running:
                await self._schedule_next_run()
    
    def trigger_immediate_scraping(self, market: Optional[str] = None, categories: Optional[list] = None):
        """Trigger immediate scraping."""
        try:
            if market:
                task = scrape_single_market_task.delay(market, categories)
                logger.info(f"Immediate single market scraping triggered for {market}, task ID: {task.id}")
            else:
                task = scrape_all_markets_task.delay(categories)
                logger.info(f"Immediate full scraping triggered, task ID: {task.id}")
            
            return task.id
            
        except Exception as e:
            logger.error(f"Error triggering immediate scraping: {e}")
            raise


async def main():
    """Main scheduler function."""
    logger.info("Starting supermarket scraper scheduler")
    
    # Test Redis connection
    try:
        redis_client.ping()
        logger.info("Redis connection successful")
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")
        return
    
    # Create and start scheduler
    scheduler = ScrapingScheduler()
    
    try:
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"Scheduler error: {e}")
    finally:
        await scheduler.stop()
        logger.info("Scheduler stopped")


if __name__ == "__main__":
    asyncio.run(main())
