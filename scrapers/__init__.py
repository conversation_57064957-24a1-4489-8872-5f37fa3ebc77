"""
Scrapers package for the supermarket scraper application.
"""

from .base_scraper import BaseScraper
from . import utils as scraper_utils

# Import utility functions from utils.py
clean_text = scraper_utils.clean_text
extract_price_from_text = scraper_utils.extract_price_from_text
extract_brand_from_name = scraper_utils.extract_brand_from_name
normalize_category = scraper_utils.normalize_category
extract_weight_or_size = scraper_utils.extract_weight_or_size
is_valid_url = scraper_utils.is_valid_url
build_absolute_url = scraper_utils.build_absolute_url
extract_promotion_info = scraper_utils.extract_promotion_info
generate_search_keywords = scraper_utils.generate_search_keywords
detect_availability_from_text = scraper_utils.detect_availability_from_text
sanitize_filename = scraper_utils.sanitize_filename
parse_date_string = scraper_utils.parse_date_string

__all__ = [
    "BaseScraper",
    "clean_text",
    "extract_price_from_text",
    "extract_brand_from_name",
    "normalize_category",
    "extract_weight_or_size",
    "is_valid_url",
    "build_absolute_url",
    "extract_promotion_info",
    "generate_search_keywords",
    "detect_availability_from_text",
    "sanitize_filename",
    "parse_date_string"
]
