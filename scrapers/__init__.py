"""
Scrapers package for the supermarket scraper application.
"""

from .base_scraper import BaseScraper
from .utils import (
    clean_text,
    extract_price_from_text,
    extract_brand_from_name,
    normalize_category,
    extract_weight_or_size,
    is_valid_url,
    build_absolute_url,
    extract_promotion_info,
    generate_search_keywords,
    detect_availability_from_text,
    sanitize_filename,
    parse_date_string
)

__all__ = [
    "BaseScraper",
    "clean_text",
    "extract_price_from_text",
    "extract_brand_from_name",
    "normalize_category",
    "extract_weight_or_size",
    "is_valid_url",
    "build_absolute_url",
    "extract_promotion_info",
    "generate_search_keywords",
    "detect_availability_from_text",
    "sanitize_filename",
    "parse_date_string"
]
