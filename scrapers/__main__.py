#!/usr/bin/env python3
"""
Scraper Service Main Module
===========================

This module runs the scraper service that listens for scraping tasks
and executes them using the enhanced scraper system.
"""

import os
import sys
import time
import logging
import asyncio
import signal
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, '/app')

from scrapers.scraper_manager import ScraperManager
from scrapers.advanced_utils import get_metrics_collector
from data.models import ScrapingConfig, MarketEnum

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_scraping_config() -> ScrapingConfig:
    """Create a ScrapingConfig from environment variables."""
    return ScrapingConfig(
        rate_limit_delay=float(os.getenv('RATE_LIMIT_DELAY', '2.0')),
        max_retries=int(os.getenv('MAX_RETRIES', '3')),
        timeout=int(os.getenv('TIMEOUT', '30')),
        headless=os.getenv('HEADLESS_BROWSER', 'true').lower() == 'true',
        user_agent_rotation=os.getenv('USER_AGENT_ROTATION', 'true').lower() == 'true',
        markets_enabled=[MarketEnum.CARREFOUR, MarketEnum.COTO, MarketEnum.JUMBO, MarketEnum.DISCO, MarketEnum.VEA],
        categories_to_scrape=[]
    )


class ScraperService:
    """Main scraper service that handles scraping tasks."""
    
    def __init__(self):
        self.config = create_scraping_config()
        self.manager = None
        self.metrics = get_metrics_collector()
        self.running = False
        self.mongodb_url = os.getenv('MONGODB_URL', '************************************************************************')
        
    def setup(self):
        """Setup the scraper service."""
        logger.info("🔧 Setting up scraper service...")
        
        try:
            # Initialize scraper manager
            self.manager = ScraperManager(self.config)
            logger.info("✅ ScraperManager initialized successfully")
            
            # Log configuration
            logger.info(f"📊 MongoDB URL: {self.mongodb_url}")
            logger.info(f"🔄 Rate limit delay: {self.config.rate_limit_delay}s")
            logger.info(f"🎭 User agent rotation: {self.config.user_agent_rotation}")
            logger.info(f"👻 Headless browser: {self.config.headless}")
            logger.info(f"🏪 Markets enabled: {[str(m) for m in self.config.markets_enabled]}")

            # Log enhanced features
            logger.info("🚀 Enhanced features enabled:")
            logger.info(f"   📈 Metrics collection: Active")
            logger.info(f"   🔄 Retry logic: Active")
            logger.info(f"   🛡️ Circuit breaker: Active")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup scraper service: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def start(self):
        """Start the scraper service."""
        logger.info("🚀 Starting scraper service...")
        
        if not self.setup():
            logger.error("❌ Failed to setup scraper service")
            return False
        
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("✅ Scraper service started successfully!")
        logger.info("📡 Listening for scraping tasks...")
        
        # Main service loop
        try:
            while self.running:
                # Check for scraping tasks (this would normally check Redis/database)
                # For now, just keep the service alive
                time.sleep(10)
                
                # Log periodic status
                if hasattr(self, '_last_status_log'):
                    if time.time() - self._last_status_log > 300:  # Every 5 minutes
                        self._log_status()
                else:
                    self._last_status_log = time.time()
                    
        except KeyboardInterrupt:
            logger.info("🛑 Received interrupt signal")
        except Exception as e:
            logger.error(f"💥 Service error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.stop()
    
    def _log_status(self):
        """Log periodic status information."""
        try:
            summary = self.metrics.get_performance_summary()
            logger.info(f"📊 Service status - Operations: {summary['total_operations']}, "
                       f"Success rate: {summary['overall_success_rate']:.1%}, "
                       f"Uptime: {summary['uptime_seconds']:.0f}s")
            self._last_status_log = time.time()
        except Exception as e:
            logger.warning(f"⚠️ Could not log status: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"🛑 Received signal {signum}, shutting down gracefully...")
        self.running = False
    
    def stop(self):
        """Stop the scraper service."""
        logger.info("🛑 Stopping scraper service...")
        self.running = False
        
        if self.manager:
            try:
                # Cleanup scraper manager if it has cleanup method
                if hasattr(self.manager, 'cleanup'):
                    self.manager.cleanup()
                logger.info("✅ ScraperManager cleaned up")
            except Exception as e:
                logger.warning(f"⚠️ Error during cleanup: {e}")
        
        logger.info("✅ Scraper service stopped")


def main():
    """Main entry point for the scraper service."""
    print("🚀 ENHANCED SCRAPER SERVICE")
    print("=" * 50)
    
    service = ScraperService()
    
    try:
        service.start()
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
