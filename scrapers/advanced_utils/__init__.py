"""
Scraping utilities for robust and reliable web scraping.
"""

from .circuit_breaker import Circuit<PERSON>reaker, CircuitBreakerConfig, ScrapingCircuitBreaker
from .retry import (
    RetryConfig, SmartRetry, retry_with_backoff,
    retry_page_load, retry_element_find, retry_data_extraction, retry_api_request,
    get_retry_config, SCRAPING_RETRY_CONFIGS
)
from .metrics import (
    MetricsCollector, PerformanceMonitor, get_metrics_collector,
    monitor_operation, monitor_scraping_operation,
    record_page_load, record_product_extraction, get_scraping_stats
)

__all__ = [
    'CircuitBreaker',
    'CircuitBreakerConfig',
    'ScrapingCircuitBreaker',
    'RetryConfig',
    'SmartRetry',
    'retry_with_backoff',
    'retry_page_load',
    'retry_element_find',
    'retry_data_extraction',
    'retry_api_request',
    'get_retry_config',
    'SCRAPING_RETRY_CONFIGS',
    'MetricsCollector',
    'PerformanceMonitor',
    'get_metrics_collector',
    'monitor_operation',
    'monitor_scraping_operation',
    'record_page_load',
    'record_product_extraction',
    'get_scraping_stats'
]
