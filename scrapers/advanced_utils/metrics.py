"""
Performance monitoring and metrics collection for scraping operations.
"""
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from threading import Lock
import statistics

logger = logging.getLogger(__name__)


@dataclass
class OperationMetric:
    """Metrics for a single operation."""
    operation: str
    duration: float
    success: bool
    timestamp: float
    url: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AggregatedMetrics:
    """Aggregated metrics for an operation type."""
    operation: str
    total_count: int
    success_count: int
    failure_count: int
    avg_duration: float
    min_duration: float
    max_duration: float
    p95_duration: float
    success_rate: float
    last_updated: float


class MetricsCollector:
    """Collects and aggregates scraping metrics."""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.lock = Lock()
        self.start_time = time.time()
    
    def record_operation(self, operation: str, duration: float, success: bool,
                        url: Optional[str] = None, error: Optional[str] = None,
                        **metadata) -> None:
        """Record metrics for an operation."""
        metric = OperationMetric(
            operation=operation,
            duration=duration,
            success=success,
            timestamp=time.time(),
            url=url,
            error=error,
            metadata=metadata
        )
        
        with self.lock:
            self.metrics[operation].append(metric)
    
    def get_aggregated_metrics(self, operation: str) -> Optional[AggregatedMetrics]:
        """Get aggregated metrics for an operation."""
        with self.lock:
            if operation not in self.metrics or not self.metrics[operation]:
                return None
            
            metrics_list = list(self.metrics[operation])
            
        total_count = len(metrics_list)
        success_count = sum(1 for m in metrics_list if m.success)
        failure_count = total_count - success_count
        
        durations = [m.duration for m in metrics_list]
        avg_duration = statistics.mean(durations)
        min_duration = min(durations)
        max_duration = max(durations)
        
        # Calculate 95th percentile
        sorted_durations = sorted(durations)
        p95_index = int(0.95 * len(sorted_durations))
        p95_duration = sorted_durations[p95_index] if sorted_durations else 0
        
        success_rate = success_count / total_count if total_count > 0 else 0
        
        return AggregatedMetrics(
            operation=operation,
            total_count=total_count,
            success_count=success_count,
            failure_count=failure_count,
            avg_duration=avg_duration,
            min_duration=min_duration,
            max_duration=max_duration,
            p95_duration=p95_duration,
            success_rate=success_rate,
            last_updated=time.time()
        )
    
    def get_all_metrics(self) -> Dict[str, AggregatedMetrics]:
        """Get aggregated metrics for all operations."""
        result = {}
        for operation in self.metrics.keys():
            metrics = self.get_aggregated_metrics(operation)
            if metrics:
                result[operation] = metrics
        return result
    
    def get_recent_failures(self, operation: str, limit: int = 10) -> List[OperationMetric]:
        """Get recent failures for an operation."""
        with self.lock:
            if operation not in self.metrics:
                return []
            
            failures = [m for m in self.metrics[operation] if not m.success]
            return list(failures)[-limit:]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get overall performance summary."""
        all_metrics = self.get_all_metrics()
        
        if not all_metrics:
            return {
                "uptime_seconds": time.time() - self.start_time,
                "total_operations": 0,
                "overall_success_rate": 0,
                "operations": {}
            }
        
        total_operations = sum(m.total_count for m in all_metrics.values())
        total_successes = sum(m.success_count for m in all_metrics.values())
        overall_success_rate = total_successes / total_operations if total_operations > 0 else 0
        
        return {
            "uptime_seconds": time.time() - self.start_time,
            "total_operations": total_operations,
            "overall_success_rate": overall_success_rate,
            "operations": {op: {
                "total": m.total_count,
                "success_rate": m.success_rate,
                "avg_duration": m.avg_duration,
                "p95_duration": m.p95_duration
            } for op, m in all_metrics.items()}
        }
    
    def reset_metrics(self, operation: Optional[str] = None) -> None:
        """Reset metrics for an operation or all operations."""
        with self.lock:
            if operation:
                if operation in self.metrics:
                    self.metrics[operation].clear()
            else:
                self.metrics.clear()
                self.start_time = time.time()


class PerformanceMonitor:
    """Context manager for monitoring operation performance."""
    
    def __init__(self, collector: MetricsCollector, operation: str, 
                 url: Optional[str] = None, **metadata):
        self.collector = collector
        self.operation = operation
        self.url = url
        self.metadata = metadata
        self.start_time = None
        self.success = False
        self.error = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.success = True
        else:
            self.success = False
            self.error = str(exc_val) if exc_val else str(exc_type)
        
        self.collector.record_operation(
            operation=self.operation,
            duration=duration,
            success=self.success,
            url=self.url,
            error=self.error,
            **self.metadata
        )
        
        # Don't suppress exceptions
        return False
    
    def mark_success(self):
        """Manually mark operation as successful."""
        self.success = True
    
    def mark_failure(self, error: str):
        """Manually mark operation as failed."""
        self.success = False
        self.error = error


# Global metrics collector instance
_global_metrics_collector = MetricsCollector()


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    return _global_metrics_collector


def monitor_operation(operation: str, url: Optional[str] = None, **metadata):
    """Decorator for monitoring operation performance."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceMonitor(_global_metrics_collector, operation, url, **metadata):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def monitor_scraping_operation(operation: str):
    """Specialized decorator for scraping operations."""
    return monitor_operation(f"scraping_{operation}")


# Convenience functions
def record_page_load(duration: float, success: bool, url: str, **metadata):
    """Record page load metrics."""
    _global_metrics_collector.record_operation(
        "page_load", duration, success, url, **metadata
    )


def record_product_extraction(duration: float, success: bool, url: str, **metadata):
    """Record product extraction metrics."""
    _global_metrics_collector.record_operation(
        "product_extraction", duration, success, url, **metadata
    )


def get_scraping_stats() -> Dict[str, Any]:
    """Get current scraping statistics."""
    return _global_metrics_collector.get_performance_summary()
