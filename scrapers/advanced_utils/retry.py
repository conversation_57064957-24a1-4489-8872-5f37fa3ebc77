"""
Advanced retry mechanisms with exponential backoff and jitter.
"""
import time
import random
import logging
from typing import Callable, Any, Optional, Type, Union, List
from dataclasses import dataclass
from functools import wraps

logger = logging.getLogger(__name__)


@dataclass
class RetryConfig:
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    exceptions: tuple = (Exception,)


class RetryExhausted(Exception):
    """Raised when all retry attempts are exhausted."""
    pass


def exponential_backoff(attempt: int, base_delay: float, max_delay: float, 
                       exponential_base: float, jitter: bool = True) -> float:
    """Calculate delay with exponential backoff and optional jitter."""
    delay = min(base_delay * (exponential_base ** attempt), max_delay)
    
    if jitter:
        # Add random jitter (±25% of delay)
        jitter_range = delay * 0.25
        delay += random.uniform(-jitter_range, jitter_range)
    
    return max(0, delay)


def retry_with_backoff(config: RetryConfig):
    """Decorator for retrying functions with exponential backoff."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    result = func(*args, **kwargs)
                    if attempt > 0:
                        logger.info(f"✅ {func.__name__} succeeded on attempt {attempt + 1}")
                    return result
                    
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        logger.error(f"❌ {func.__name__} failed after {config.max_attempts} attempts: {e}")
                        break
                    
                    delay = exponential_backoff(
                        attempt, config.base_delay, config.max_delay,
                        config.exponential_base, config.jitter
                    )
                    
                    logger.warning(f"⚠️ {func.__name__} attempt {attempt + 1} failed: {e}. "
                                 f"Retrying in {delay:.2f}s...")
                    time.sleep(delay)
            
            raise RetryExhausted(f"All {config.max_attempts} attempts failed. Last error: {last_exception}")
        
        return wrapper
    return decorator


class SmartRetry:
    """Smart retry mechanism with adaptive strategies."""
    
    def __init__(self):
        self.failure_history = {}
        self.success_history = {}
    
    def get_adaptive_config(self, operation: str, url: str = "") -> RetryConfig:
        """Get adaptive retry configuration based on historical performance."""
        key = f"{operation}:{url}" if url else operation
        
        # Get failure rate for this operation/URL
        failures = self.failure_history.get(key, 0)
        successes = self.success_history.get(key, 0)
        total = failures + successes
        
        if total == 0:
            # No history, use default config
            return RetryConfig()
        
        failure_rate = failures / total
        
        # Adapt retry strategy based on failure rate
        if failure_rate > 0.7:  # High failure rate
            return RetryConfig(
                max_attempts=5,
                base_delay=2.0,
                max_delay=120.0,
                exponential_base=2.5
            )
        elif failure_rate > 0.3:  # Medium failure rate
            return RetryConfig(
                max_attempts=4,
                base_delay=1.5,
                max_delay=60.0,
                exponential_base=2.0
            )
        else:  # Low failure rate
            return RetryConfig(
                max_attempts=2,
                base_delay=0.5,
                max_delay=30.0,
                exponential_base=1.5
            )
    
    def record_success(self, operation: str, url: str = ""):
        """Record a successful operation."""
        key = f"{operation}:{url}" if url else operation
        self.success_history[key] = self.success_history.get(key, 0) + 1
    
    def record_failure(self, operation: str, url: str = ""):
        """Record a failed operation."""
        key = f"{operation}:{url}" if url else operation
        self.failure_history[key] = self.failure_history.get(key, 0) + 1
    
    def execute_with_retry(self, func: Callable, operation: str, 
                          url: str = "", *args, **kwargs) -> Any:
        """Execute function with adaptive retry strategy."""
        config = self.get_adaptive_config(operation, url)
        
        @retry_with_backoff(config)
        def wrapped_func():
            return func(*args, **kwargs)
        
        try:
            result = wrapped_func()
            self.record_success(operation, url)
            return result
        except Exception as e:
            self.record_failure(operation, url)
            raise e


# Predefined retry configurations for common scraping operations
SCRAPING_RETRY_CONFIGS = {
    "page_load": RetryConfig(
        max_attempts=3,
        base_delay=2.0,
        max_delay=30.0,
        exceptions=(Exception,)
    ),
    "element_find": RetryConfig(
        max_attempts=5,
        base_delay=0.5,
        max_delay=10.0,
        exceptions=(Exception,)
    ),
    "data_extraction": RetryConfig(
        max_attempts=2,
        base_delay=1.0,
        max_delay=15.0,
        exceptions=(Exception,)
    ),
    "api_request": RetryConfig(
        max_attempts=4,
        base_delay=1.0,
        max_delay=60.0,
        exceptions=(Exception,)
    )
}


def get_retry_config(operation: str) -> RetryConfig:
    """Get predefined retry configuration for operation."""
    return SCRAPING_RETRY_CONFIGS.get(operation, RetryConfig())


# Convenience decorators
def retry_page_load(func):
    """Decorator for page loading operations."""
    return retry_with_backoff(get_retry_config("page_load"))(func)


def retry_element_find(func):
    """Decorator for element finding operations."""
    return retry_with_backoff(get_retry_config("element_find"))(func)


def retry_data_extraction(func):
    """Decorator for data extraction operations."""
    return retry_with_backoff(get_retry_config("data_extraction"))(func)


def retry_api_request(func):
    """Decorator for API request operations."""
    return retry_with_backoff(get_retry_config("api_request"))(func)
