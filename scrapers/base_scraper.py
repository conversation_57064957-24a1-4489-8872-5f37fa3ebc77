"""
Base scraper class with common functionality for all supermarket scrapers.
"""

import asyncio
import logging
import random
import time
import sys
import os
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Set
from urllib.parse import urljoin, urlparse
from urllib.robotparser import RobotFileParser

import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent

# Add project root to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.models import Product, MarketEnum, ScrapingSession, ScrapingConfig
from data.database import db_manager

# Import scraping utilities
try:
    from .advanced_utils import (
        ScrapingCircuitBreaker, SmartRetry, retry_page_load,
        retry_element_find, retry_data_extraction,
        get_metrics_collector, PerformanceMonitor, monitor_scraping_operation
    )
except ImportError:
    # Fallback if utils not available
    logger.warning("Scraping utilities not available, using basic error handling")
    ScrapingCircuitBreaker = None
    SmartRetry = None
    retry_page_load = lambda f: f
    retry_element_find = lambda f: f
    retry_data_extraction = lambda f: f
    get_metrics_collector = lambda: None
    PerformanceMonitor = None
    monitor_scraping_operation = lambda op: lambda f: f

logger = logging.getLogger(__name__)


class BaseScraper(ABC):
    """Base class for all supermarket scrapers."""
    
    def __init__(self, market: MarketEnum, config: ScrapingConfig):
        self.market = market
        self.config = config
        self.session = requests.Session()
        self.driver: Optional[webdriver.Chrome] = None
        self.user_agent = UserAgent()
        self.scraped_urls: Set[str] = set()
        self.session_id = f"{market}_{int(time.time())}"
        self.scraping_session: Optional[ScrapingSession] = None

        # Initialize logger
        self.logger = logging.getLogger(f"{__name__}.{market.value}")

        # Rate limiting
        self.last_request_time = 0

        # Initialize advanced error handling and monitoring
        self.circuit_breaker = ScrapingCircuitBreaker() if ScrapingCircuitBreaker else None
        self.smart_retry = SmartRetry() if SmartRetry else None
        self.metrics_collector = get_metrics_collector() if get_metrics_collector else None

        # Setup session headers
        self._setup_session()
    
    def _setup_session(self):
        """Setup HTTP session with comprehensive headers and configuration."""
        headers = {
            'User-Agent': self.user_agent.random if self.config.user_agent_rotation else self.user_agent.chrome,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'es-AR,es;q=0.9,en;q=0.8,en-US;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }
        self.session.headers.update(headers)

        # Configure session for better performance and reliability
        self.session.timeout = self.config.timeout
        self.session.max_redirects = 10

        # Add retry strategy
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _setup_driver(self) -> webdriver.Chrome:
        """Setup Selenium Chrome driver with comprehensive container-optimized options."""
        import os
        import subprocess
        import time

        # Start Xvfb virtual display for Docker containers
        try:
            # Check if we're in a Docker container
            if os.path.exists('/.dockerenv'):
                self.logger.info("🖥️ Starting Xvfb virtual display for Docker container")
                subprocess.Popen(['Xvfb', ':99', '-screen', '0', '1920x1080x24'],
                               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                os.environ['DISPLAY'] = ':99'
                import time
                time.sleep(2)  # Give Xvfb time to start
        except Exception as e:
            self.logger.warning(f"Could not start Xvfb: {e}")

        chrome_options = Options()

        # Core headless and container options
        if self.config.headless:
            chrome_options.add_argument('--headless=new')  # Use new headless mode

        # Absolute minimal Chrome options that work in Docker
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        chrome_options.add_argument('--single-process')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-default-apps')
        chrome_options.add_argument('--no-first-run')
        chrome_options.add_argument('--disable-sync')
        chrome_options.add_argument('--disable-translate')
        chrome_options.add_argument('--mute-audio')
        chrome_options.add_argument('--disable-logging')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument(f'--user-data-dir=/tmp/chrome-{int(time.time())}')

        # Memory and performance optimization
        chrome_options.add_argument('--memory-pressure-off')
        chrome_options.add_argument('--max_old_space_size=4096')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-features=TranslateUI')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')

        # Anti-detection and stealth options
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')  # Faster loading
        chrome_options.add_argument('--disable-javascript')  # We'll enable selectively
        chrome_options.add_argument('--disable-translate')
        chrome_options.add_argument('--hide-scrollbars')
        chrome_options.add_argument('--mute-audio')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')

        # Network and security options
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument('--ignore-certificate-errors-spki-list')
        chrome_options.add_argument('--disable-client-side-phishing-detection')

        # Experimental options for better stealth
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("detach", True)

        # User agent configuration
        if self.config.user_agent_rotation:
            user_agent = self.user_agent.random
        else:
            # Use a realistic, recent user agent
            user_agent = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

        chrome_options.add_argument(f'--user-agent={user_agent}')

        # Prefs for additional optimization
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,
                "media_stream": 2,
                "geolocation": 2
            },
            "profile.managed_default_content_settings": {
                "images": 2  # Block images for faster loading
            }
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Increase timeouts and improve loading
        chrome_options.add_argument('--timeout=60000')
        chrome_options.add_argument('--enable-features=NetworkService,NetworkServiceLogging')

        # Try multiple driver initialization strategies with comprehensive fallbacks
        driver = None
        last_error = None

        # Strategy 1: Use system chromedriver first (most reliable in containers)
        try:
            logger.info("Attempting to use system chromedriver at /usr/local/bin/chromedriver")
            service = Service('/usr/local/bin/chromedriver')
            driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("✅ Successfully initialized Chrome driver with system chromedriver")
        except Exception as e:
            logger.warning(f"❌ System chromedriver failed: {e}")
            last_error = e

        # Strategy 2: Try system PATH chromedriver
        if driver is None:
            try:
                logger.info("Attempting to use chromedriver from system PATH")
                service = Service()  # Uses system PATH
                driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("✅ Successfully initialized Chrome driver from PATH")
            except Exception as e:
                logger.warning(f"❌ PATH chromedriver failed: {e}")
                last_error = e

        # Strategy 3: Fallback to WebDriver Manager (only if others fail)
        if driver is None:
            try:
                logger.info("Attempting to use WebDriver Manager as fallback")
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("✅ Successfully initialized Chrome driver with WebDriver Manager")
            except Exception as e:
                logger.warning(f"❌ WebDriver Manager failed: {e}")
                last_error = e

        # Strategy 4: Try without explicit service (let Selenium auto-detect)
        if driver is None:
            try:
                logger.info("Attempting Selenium auto-detection")
                driver = webdriver.Chrome(options=chrome_options)
                logger.info("✅ Successfully initialized Chrome driver with auto-detection")
            except Exception as e:
                logger.error(f"❌ Auto-detection also failed: {e}")
                last_error = e

        if driver is None:
            raise WebDriverException(f"❌ Could not initialize Chrome driver with any method. Last error: {last_error}")

        # Configure driver for optimal operation
        try:
            # Apply stealth configurations
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.execute_script("Object.defineProperty(navigator, 'permissions', {get: () => undefined})")
            driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['es-AR', 'es', 'en']})")

            # Set optimal timeouts
            driver.implicitly_wait(10)
            driver.set_page_load_timeout(60)
            driver.set_script_timeout(30)

            logger.info("✅ Applied stealth configurations and timeouts")

        except Exception as e:
            logger.warning(f"⚠️ Could not apply some stealth configurations: {e}")

        return driver
    
    def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.config.rate_limit_delay:
            sleep_time = self.config.rate_limit_delay - time_since_last_request
            # Add some randomness to avoid detection
            sleep_time += random.uniform(0, 0.5)
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _check_robots_txt(self, url: str) -> bool:
        """Check if URL is allowed by robots.txt."""
        # Temporarily disable robots.txt checking for testing
        # TODO: Re-enable with proper user agent and rate limiting
        logger.debug(f"Skipping robots.txt check for {url} (disabled for testing)")
        return True

        # Original robots.txt checking code (commented out)
        # try:
        #     parsed_url = urlparse(url)
        #     robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
        #
        #     rp = RobotFileParser()
        #     rp.set_url(robots_url)
        #     rp.read()
        #
        #     user_agent = self.session.headers.get('User-Agent', '*')
        #     return rp.can_fetch(user_agent, url)
        #
        # except Exception as e:
        #     logger.warning(f"Could not check robots.txt for {url}: {e}")
        #     return True  # Allow if we can't check
    
    def _make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """Make HTTP request with rate limiting and error handling."""
        if not self._check_robots_txt(url):
            logger.warning(f"URL blocked by robots.txt: {url}")
            return None
        
        self._rate_limit()
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.get(url, timeout=self.config.timeout, **kwargs)
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.max_retries}): {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"All retry attempts failed for {url}")
        
        return None
    
    def _navigate_with_driver(self, url: str) -> bool:
        """Navigate to URL using Selenium driver."""
        if not self._check_robots_txt(url):
            logger.warning(f"URL blocked by robots.txt: {url}")
            return False
        
        self._rate_limit()
        
        try:
            if not self.driver:
                self.driver = self._setup_driver()
            
            self.driver.get(url)
            
            # Wait for page to load with increased timeout
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            return True
            
        except (TimeoutException, WebDriverException) as e:
            logger.error(f"Failed to navigate to {url}: {e}")
            return False
    
    def _extract_price(self, price_text: str) -> Optional[float]:
        """Extract numeric price from text."""
        if not price_text:
            return None
        
        try:
            # Remove currency symbols and common text
            price_clean = price_text.replace('$', '').replace(',', '').replace('.', '')
            price_clean = ''.join(filter(str.isdigit, price_clean))
            
            if price_clean:
                # Assume last two digits are cents
                if len(price_clean) > 2:
                    return float(price_clean[:-2] + '.' + price_clean[-2:])
                else:
                    return float(price_clean)
            
        except (ValueError, AttributeError):
            pass
        
        return None
    
    def _normalize_product_name(self, name: str) -> str:
        """Normalize product name for consistency."""
        if not name:
            return ""
        
        # Remove extra whitespace and normalize
        name = ' '.join(name.split())
        
        # Remove common prefixes/suffixes that don't add value
        prefixes_to_remove = ['producto:', 'item:', 'art:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):].strip()
        
        return name.title()
    
    async def _start_scraping_session(self):
        """Initialize scraping session tracking."""
        self.scraping_session = ScrapingSession(
            session_id=self.session_id,
            market=self.market,
            scraper_config=self.config.dict()
        )
        
        await db_manager.save_scraping_session(self.scraping_session)
        logger.info(f"Started scraping session {self.session_id} for {self.market}")
    
    async def _update_scraping_session(self, **kwargs):
        """Update scraping session with new data."""
        if self.scraping_session:
            for key, value in kwargs.items():
                if hasattr(self.scraping_session, key):
                    setattr(self.scraping_session, key, value)
            
            await db_manager.save_scraping_session(self.scraping_session)
    
    async def _finish_scraping_session(self, status: str = "completed"):
        """Finalize scraping session."""
        if self.scraping_session:
            self.scraping_session.status = status
            self.scraping_session.completed_at = time.time()
            
            await db_manager.save_scraping_session(self.scraping_session)
            logger.info(f"Finished scraping session {self.session_id} with status: {status}")
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                logger.warning(f"Error closing driver: {e}")
        
        if self.session:
            self.session.close()

    def try_requests_scraping(self, url: str) -> Optional[str]:
        """Try to scrape a page using requests only (faster, no JS)."""
        try:
            self._rate_limit()
            response = self.session.get(url, timeout=self.config.timeout)
            response.raise_for_status()

            # Check if the page has meaningful content (not just JS loading screen)
            content = response.text.lower()
            if any(indicator in content for indicator in [
                'product', 'precio', 'price', 'item', 'articulo',
                'categoria', 'category', 'marca', 'brand'
            ]):
                logger.info(f"✅ Successfully scraped {url} with requests")
                return response.text
            else:
                logger.info(f"⚠️ Page {url} appears to be JS-only, will need Selenium")
                return None

        except Exception as e:
            logger.warning(f"❌ Requests scraping failed for {url}: {e}")
            return None

    def detect_api_endpoints(self, base_url: str) -> List[str]:
        """Detect potential API endpoints for the site."""
        potential_apis = []
        common_api_paths = [
            '/api/products', '/api/catalog', '/api/search',
            '/api/v1/products', '/api/v2/products',
            '/catalog/api', '/search/api',
            '/graphql', '/api/graphql',
            '/_next/data', '/api/items'
        ]

        for path in common_api_paths:
            api_url = f"{base_url.rstrip('/')}{path}"
            try:
                response = self.session.head(api_url, timeout=5)
                if response.status_code in [200, 405]:  # 405 means method not allowed but endpoint exists
                    potential_apis.append(api_url)
                    logger.info(f"🔍 Found potential API endpoint: {api_url}")
            except:
                continue

        return potential_apis

    def smart_page_load(self, url: str) -> str:
        """Intelligently load a page using the best available method with error handling."""
        if self.circuit_breaker and self.circuit_breaker.site_breaker.is_open:
            raise Exception(f"Site circuit breaker is open for {url}")

        # Strategy 1: Try requests first (faster)
        try:
            if self.smart_retry:
                content = self.smart_retry.execute_with_retry(
                    self.try_requests_scraping, "requests_scraping", url, url
                )
            else:
                content = self.try_requests_scraping(url)

            if content:
                return content
        except Exception as e:
            logger.warning(f"⚠️ Requests scraping failed for {url}: {e}")

        # Strategy 2: Use Selenium if requests failed or page is JS-heavy
        return self._selenium_page_load(url)

    def _selenium_page_load(self, url: str) -> str:
        """Load page with Selenium with circuit breaker protection."""
        if self.circuit_breaker:
            breaker = self.circuit_breaker.get_breaker("page_load")
            return breaker.call(self._selenium_page_load_impl, url)
        else:
            return self._selenium_page_load_impl(url)

    @retry_page_load
    def _selenium_page_load_impl(self, url: str) -> str:
        """Implementation of Selenium page loading with retries."""
        if not self.driver:
            self.driver = self._setup_driver()

        logger.info(f"🌐 Loading {url} with Selenium")
        self.driver.get(url)

        # Wait for page to load with multiple strategies
        try:
            # Strategy 1: Wait for document ready
            WebDriverWait(self.driver, 15).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )

            # Strategy 2: Wait for body to be present
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Strategy 3: Wait for any content indicators
            try:
                WebDriverWait(self.driver, 5).until(
                    lambda d: len(d.find_elements(By.CSS_SELECTOR,
                        "div, span, p, article, section")) > 0
                )
            except TimeoutException:
                logger.warning("⚠️ No content elements found, proceeding anyway")

            # Additional wait for dynamic content
            time.sleep(2)

        except TimeoutException as e:
            logger.warning(f"⚠️ Page load timeout for {url}: {e}")
            # Continue anyway, might still have content

        content = self.driver.page_source
        if len(content) < 1000:  # Suspiciously small page
            raise Exception(f"Page content too small ({len(content)} chars), likely failed to load")

        logger.info(f"✅ Successfully loaded {url} with Selenium ({len(content)} chars)")
        return content

    @abstractmethod
    async def scrape_products(self, categories: Optional[List[str]] = None) -> List[Product]:
        """Scrape products from the supermarket website."""
        pass
    
    @abstractmethod
    def get_product_urls(self, category: Optional[str] = None) -> List[str]:
        """Get list of product URLs to scrape."""
        pass
    
    @abstractmethod
    def extract_product_data(self, url: str) -> Optional[Product]:
        """Extract product data from a specific URL."""
        pass
    
    async def run_scraping(self, categories: Optional[List[str]] = None) -> int:
        """Run the complete scraping process."""
        try:
            await self._start_scraping_session()
            
            products = await self.scrape_products(categories)
            
            if products:
                saved_count = await db_manager.save_products_batch(products)
                await self._update_scraping_session(
                    products_found=len(products),
                    products_new=saved_count
                )
                
                logger.info(f"Scraped and saved {saved_count} products from {self.market}")
                await self._finish_scraping_session("completed")
                return saved_count
            else:
                await self._finish_scraping_session("no_products_found")
                return 0
                
        except Exception as e:
            logger.error(f"Scraping failed for {self.market}: {e}")
            await self._finish_scraping_session("failed")
            return 0
        
        finally:
            self.cleanup()
