"""
Coto Digital scraper implementation.
Based on analysis of cotodigital3.com.ar - Angular SPA requiring Selenium.
"""

import logging
import time
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys

from data.models import Product, MarketEnum, PriceInfo, ProductPromotion, ProductAvailability
from .base_scraper import BaseScraper
from .utils import (
    clean_text, extract_price_from_text, extract_brand_from_name,
    normalize_category, extract_weight_or_size, generate_search_keywords,
    detect_availability_from_text, extract_promotion_info
)

logger = logging.getLogger(__name__)


class CotoScraper(BaseScraper):
    """Scraper for Coto Digital (cotodigital3.com.ar)."""
    
    def __init__(self, config):
        super().__init__(MarketEnum.COTO, config)
        self.base_url = "https://www.cotodigital3.com.ar"
        self.search_url = "https://www.cotodigital3.com.ar/sitios/cdigi/browse"
        
        # Category mappings based on site structure
        self.categories = {
            "ofertas": "Ofertas",
            "almacen": "Almacén", 
            "bebidas": "Bebidas",
            "frescos": "Frescos",
            "congelados": "Congelados",
            "limpieza": "Limpieza",
            "perfumeria": "Perfumería",
            "electro": "Electro",
            "textil": "Textil",
            "hogar": "Hogar",
            "aire-libre": "Aire Libre"
        }
    
    def get_product_urls(self, category: Optional[str] = None) -> List[str]:
        """Get product URLs from category pages or search."""
        urls = []
        
        try:
            if not self.driver:
                self.driver = self._setup_driver()
            
            if category and category in self.categories:
                # Navigate to specific category
                category_url = f"{self.base_url}/sitios/cdigi/categoria/{category}"
                if self._navigate_with_driver(category_url):
                    urls.extend(self._extract_product_urls_from_page())
            else:
                # Get URLs from all categories
                for cat_key in self.categories.keys():
                    category_url = f"{self.base_url}/sitios/cdigi/categoria/{cat_key}"
                    if self._navigate_with_driver(category_url):
                        urls.extend(self._extract_product_urls_from_page())
                        
                        # Handle pagination
                        urls.extend(self._handle_pagination())
        
        except Exception as e:
            logger.error(f"Error getting product URLs: {e}")
        
        return list(set(urls))  # Remove duplicates
    
    def _extract_product_urls_from_page(self) -> List[str]:
        """Extract product URLs from current page."""
        urls = []
        
        try:
            # Wait for products to load with increased timeout
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.CLASS_NAME, "product-item"))
            )
            
            # Find product links - updated selectors based on actual Coto structure
            product_selectors = [
                "a[href*='product']",  # This works based on our test!
                "a[href*='/productos/']",
                "a[href*='/producto/']",
                ".product-item a",
                ".vtex-product-summary-2-x-clearLink",
                "[data-testid='product-link']"
            ]
            
            for selector in product_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    logger.info(f"Found {len(elements)} elements with selector: {selector}")
                    for element in elements[:20]:  # Limit to first 20 for testing
                        try:
                            href = element.get_attribute('href')
                            if href and ('product' in href or '/producto' in href):
                                urls.append(href)
                        except Exception:
                            continue
                    
                    if urls:  # If we found URLs with this selector, break
                        break
                        
                except NoSuchElementException:
                    continue
        
        except TimeoutException:
            logger.warning("Timeout waiting for products to load, trying alternative selectors")
            # Try alternative selectors if main one fails
            try:
                alternative_selectors = [
                    ".product",
                    ".item",
                    "[data-product]",
                    "article",
                    ".card"
                ]

                for selector in alternative_selectors:
                    try:
                        WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        logger.info(f"Found products using alternative selector: {selector}")

                        # Extract URLs using the alternative selector
                        elements = self.driver.find_elements(By.CSS_SELECTOR, f"{selector} a")
                        for element in elements[:10]:  # Limit to first 10
                            try:
                                href = element.get_attribute('href')
                                if href and 'product' in href.lower():
                                    urls.append(href)
                            except Exception:
                                continue
                        break
                    except TimeoutException:
                        continue

            except Exception as e:
                logger.error(f"Error trying alternative selectors: {e}")
        
        return urls
    
    def _handle_pagination(self) -> List[str]:
        """Handle pagination to get more product URLs."""
        urls = []
        page = 2
        max_pages = 10  # Limit to avoid infinite loops
        
        while page <= max_pages:
            try:
                # Look for next page button
                next_selectors = [
                    ".vtex-button[aria-label='Next']",
                    ".pagination-next",
                    "[data-testid='next-page']",
                    "a[aria-label='Siguiente']"
                ]
                
                next_button = None
                for selector in next_selectors:
                    try:
                        next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if next_button.is_enabled():
                            break
                    except NoSuchElementException:
                        continue
                
                if next_button and next_button.is_enabled():
                    self.driver.execute_script("arguments[0].click();", next_button)
                    time.sleep(3)  # Wait for page to load
                    
                    # Extract URLs from new page
                    page_urls = self._extract_product_urls_from_page()
                    if page_urls:
                        urls.extend(page_urls)
                        page += 1
                    else:
                        break
                else:
                    break
                    
            except Exception as e:
                logger.warning(f"Error handling pagination on page {page}: {e}")
                break
        
        return urls
    
    def extract_product_data(self, url: str) -> Optional[Product]:
        """Extract product data from a specific product URL."""
        try:
            if not self._navigate_with_driver(url):
                return None
            
            # Wait for product details to load with increased timeout
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Extract product information
            name = self._extract_product_name()
            if not name:
                logger.warning(f"Could not extract product name from {url}")
                return None
            
            price_info = self._extract_price_info()
            if not price_info:
                logger.warning(f"Could not extract price from {url}")
                return None
            
            # Extract other product details
            brand = self._extract_brand()
            category = self._extract_category()
            image_url = self._extract_image_url()
            description = self._extract_description()
            availability = self._extract_availability()
            promotions = self._extract_promotions()
            weight = self._extract_weight()
            
            # Generate search keywords
            keywords = generate_search_keywords(name, brand, category)
            
            product = Product(
                name=clean_text(name),
                brand=brand,
                category=normalize_category(category) if category else None,
                price=price_info,
                market=self.market,
                market_product_id=self._extract_product_id(url),
                url=url,
                image_url=image_url,
                availability=availability,
                promotions=promotions,
                description=description,
                weight=weight,
                search_keywords=keywords
            )
            
            return product
            
        except Exception as e:
            logger.error(f"Error extracting product data from {url}: {e}")
            return None
    
    def _extract_product_name(self) -> Optional[str]:
        """Extract product name from page."""
        selectors = [
            ".vtex-store-components-3-x-productBrand",
            ".product-name",
            "h1.product-title",
            "[data-testid='product-name']",
            ".vtex-product-summary-2-x-productBrand"
        ]
        
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                name = element.text.strip()
                if name:
                    return name
            except NoSuchElementException:
                continue
        
        return None
    
    def _extract_price_info(self) -> Optional[PriceInfo]:
        """Extract price information from page."""
        try:
            # Current price selectors
            price_selectors = [
                ".vtex-product-price-1-x-sellingPrice",
                ".price-current",
                ".selling-price",
                "[data-testid='price-current']"
            ]
            
            current_price = None
            for selector in price_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    price_text = element.text.strip()
                    current_price = extract_price_from_text(price_text)
                    if current_price:
                        break
                except NoSuchElementException:
                    continue
            
            if not current_price:
                return None
            
            # Previous price (if on sale)
            previous_price = None
            previous_selectors = [
                ".vtex-product-price-1-x-listPrice",
                ".price-before",
                ".list-price",
                "[data-testid='price-before']"
            ]
            
            for selector in previous_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    price_text = element.text.strip()
                    previous_price = extract_price_from_text(price_text)
                    if previous_price:
                        break
                except NoSuchElementException:
                    continue
            
            # Calculate discount percentage
            discount_percentage = None
            if previous_price and previous_price > current_price:
                discount_percentage = round(((previous_price - current_price) / previous_price) * 100, 2)
            
            return PriceInfo(
                current=current_price,
                previous=previous_price,
                currency="ARS",
                discount_percentage=discount_percentage
            )
            
        except Exception as e:
            logger.error(f"Error extracting price info: {e}")
            return None
    
    def _extract_brand(self) -> Optional[str]:
        """Extract brand from page."""
        selectors = [
            ".vtex-product-summary-2-x-productBrandName",
            ".product-brand",
            "[data-testid='product-brand']"
        ]
        
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                brand = element.text.strip()
                if brand:
                    return brand
            except NoSuchElementException:
                continue
        
        # Try to extract from product name
        name = self._extract_product_name()
        if name:
            return extract_brand_from_name(name)
        
        return None
    
    def _extract_category(self) -> Optional[str]:
        """Extract category from breadcrumbs or page structure."""
        selectors = [
            ".breadcrumb a",
            ".vtex-breadcrumb-1-x-link",
            "[data-testid='breadcrumb'] a"
        ]
        
        for selector in selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if len(elements) > 1:  # Skip "Home" link
                    category = elements[1].text.strip()
                    if category and category.lower() != "home":
                        return category
            except NoSuchElementException:
                continue
        
        return None
    
    def _extract_image_url(self) -> Optional[str]:
        """Extract product image URL."""
        selectors = [
            ".vtex-store-components-3-x-productImageTag",
            ".product-image img",
            "[data-testid='product-image'] img"
        ]
        
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                src = element.get_attribute('src')
                if src:
                    return src if src.startswith('http') else urljoin(self.base_url, src)
            except NoSuchElementException:
                continue
        
        return None
    
    def _extract_description(self) -> Optional[str]:
        """Extract product description."""
        selectors = [
            ".product-description",
            ".vtex-product-summary-2-x-productDescription",
            "[data-testid='product-description']"
        ]
        
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                description = element.text.strip()
                if description:
                    return clean_text(description)
            except NoSuchElementException:
                continue
        
        return None
    
    def _extract_availability(self) -> ProductAvailability:
        """Extract product availability status."""
        try:
            # Look for availability indicators
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
            availability_text = detect_availability_from_text(page_text)
            
            if availability_text == "out_of_stock":
                return ProductAvailability.OUT_OF_STOCK
            elif availability_text == "limited_stock":
                return ProductAvailability.LIMITED_STOCK
            elif availability_text == "in_stock":
                return ProductAvailability.IN_STOCK
            
        except Exception as e:
            logger.warning(f"Error extracting availability: {e}")
        
        return ProductAvailability.UNKNOWN
    
    def _extract_promotions(self) -> List[ProductPromotion]:
        """Extract promotion information."""
        promotions = []
        
        try:
            # Look for promotion badges/labels
            promo_selectors = [
                ".promotion-badge",
                ".offer-label",
                ".vtex-product-summary-2-x-promotionBadge"
            ]
            
            for selector in promo_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        promo_text = element.text.strip()
                        if promo_text:
                            promo_info = extract_promotion_info(promo_text)
                            if promo_info:
                                promotions.append(ProductPromotion(
                                    type=promo_info['type'],
                                    description=promo_info['description']
                                ))
                except NoSuchElementException:
                    continue
        
        except Exception as e:
            logger.warning(f"Error extracting promotions: {e}")
        
        return promotions
    
    def _extract_weight(self) -> Optional[str]:
        """Extract product weight/size information."""
        try:
            name = self._extract_product_name()
            description = self._extract_description()
            
            text_to_search = f"{name or ''} {description or ''}"
            return extract_weight_or_size(text_to_search)
            
        except Exception as e:
            logger.warning(f"Error extracting weight: {e}")
            return None
    
    def _extract_product_id(self, url: str) -> Optional[str]:
        """Extract product ID from URL."""
        try:
            # Coto URLs typically contain product ID
            if '/producto/' in url:
                parts = url.split('/producto/')
                if len(parts) > 1:
                    return parts[1].split('/')[0]
        except Exception:
            pass
        
        return url  # Fallback to URL as ID
    
    async def scrape_products(self, categories: Optional[List[str]] = None) -> List[Product]:
        """Scrape products from Coto Digital."""
        products = []
        
        try:
            # Get product URLs
            if categories:
                all_urls = []
                for category in categories:
                    if category in self.categories:
                        urls = self.get_product_urls(category)
                        all_urls.extend(urls)
            else:
                all_urls = self.get_product_urls()
            
            logger.info(f"Found {len(all_urls)} product URLs to scrape")
            
            # Extract product data
            for i, url in enumerate(all_urls):
                if url in self.scraped_urls:
                    continue
                
                logger.info(f"Scraping product {i+1}/{len(all_urls)}: {url}")
                
                product = self.extract_product_data(url)
                if product:
                    products.append(product)
                    self.scraped_urls.add(url)
                
                # Update session stats
                if self.scraping_session:
                    await self._update_scraping_session(
                        products_found=len(products)
                    )
                
                # Respect rate limiting
                time.sleep(self.config.rate_limit_delay)
        
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
        
        return products
