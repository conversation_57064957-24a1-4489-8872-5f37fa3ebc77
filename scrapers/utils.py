"""
Utility functions for scrapers.
"""

import re
import logging
from typing import Optional, List, Dict, Any
from urllib.parse import urljoin, urlparse
from datetime import datetime

logger = logging.getLogger(__name__)


def clean_text(text: str) -> str:
    """Clean and normalize text content."""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters that might cause issues
    text = re.sub(r'[^\w\s\-\.\,\(\)\%\$]', '', text)
    
    return text


def extract_price_from_text(text: str) -> Optional[float]:
    """Extract price from text using various patterns."""
    if not text:
        return None
    
    # Common price patterns for Argentine pesos
    patterns = [
        r'\$\s*(\d{1,3}(?:\.\d{3})*(?:,\d{2})?)',  # $1.234,56 or $1234,56
        r'(\d{1,3}(?:\.\d{3})*(?:,\d{2})?)\s*\$',  # 1.234,56$ or 1234,56$
        r'(\d{1,3}(?:\.\d{3})*(?:,\d{2})?)',       # 1.234,56 or 1234,56
        r'\$\s*(\d+(?:,\d{2})?)',                  # $1234,56
        r'(\d+(?:,\d{2})?)\s*pesos?',              # 1234,56 pesos
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            price_str = match.group(1)
            try:
                # Convert Argentine format to float
                # Replace dots (thousands separator) and comma (decimal separator)
                price_str = price_str.replace('.', '').replace(',', '.')
                return float(price_str)
            except ValueError:
                continue
    
    return None


def extract_brand_from_name(product_name: str) -> Optional[str]:
    """Extract brand from product name using common patterns."""
    if not product_name:
        return None
    
    # Common brand patterns
    brand_patterns = [
        r'^([A-Z][A-Z\s&]+?)\s+',  # Brand at start in caps
        r'\b([A-Z][a-z]+)\s+',     # Capitalized word
    ]
    
    for pattern in brand_patterns:
        match = re.search(pattern, product_name)
        if match:
            brand = match.group(1).strip()
            if len(brand) > 1 and brand.upper() != brand:  # Avoid single letters and all caps
                return brand
    
    return None


def normalize_category(category: str) -> str:
    """Normalize category names."""
    if not category:
        return ""
    
    # Convert to title case and clean
    category = clean_text(category).title()
    
    # Common category mappings
    category_mappings = {
        'Almacen': 'Almacén',
        'Lacteos': 'Lácteos',
        'Carniceria': 'Carnicería',
        'Panaderia': 'Panadería',
        'Verduleria': 'Verdulería',
        'Limpieza': 'Limpieza',
        'Perfumeria': 'Perfumería',
        'Bebidas': 'Bebidas',
        'Congelados': 'Congelados',
        'Frescos': 'Frescos',
    }
    
    return category_mappings.get(category, category)


def extract_weight_or_size(text: str) -> Optional[str]:
    """Extract weight or size information from text."""
    if not text:
        return None
    
    # Weight/size patterns
    patterns = [
        r'(\d+(?:,\d+)?\s*(?:kg|g|ml|l|cc|cm|mm|m))\b',
        r'(\d+(?:,\d+)?\s*(?:gramos?|kilos?|litros?|mililitros?))\b',
        r'(\d+\s*x\s*\d+(?:\s*(?:g|ml|cm))?)',  # Pack sizes like "6 x 500ml"
        r'(\d+\s*unidades?)',
        r'(\d+\s*pack)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    return None


def is_valid_url(url: str) -> bool:
    """Check if URL is valid."""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def build_absolute_url(base_url: str, relative_url: str) -> str:
    """Build absolute URL from base and relative URLs."""
    if is_valid_url(relative_url):
        return relative_url
    
    return urljoin(base_url, relative_url)


def extract_promotion_info(text: str) -> Optional[Dict[str, Any]]:
    """Extract promotion information from text."""
    if not text:
        return None
    
    text_lower = text.lower()
    
    # Common promotion patterns
    promotion_patterns = {
        'discount': r'(\d+)%\s*(?:off|desc|descuento)',
        '2x1': r'2\s*x\s*1',
        '3x2': r'3\s*x\s*2',
        'buy_get': r'(?:compra|lleva)\s*(\d+)\s*(?:paga|lleva)\s*(\d+)',
        'free_shipping': r'(?:envio|envío)\s*(?:gratis|gratuito)',
    }
    
    for promo_type, pattern in promotion_patterns.items():
        match = re.search(pattern, text_lower)
        if match:
            return {
                'type': promo_type,
                'description': text.strip(),
                'match': match.groups() if match.groups() else None
            }
    
    return None


def generate_search_keywords(product_name: str, brand: str = None, category: str = None) -> List[str]:
    """Generate search keywords for a product."""
    keywords = []
    
    if product_name:
        # Add full name
        keywords.append(product_name.lower())
        
        # Add individual words (longer than 2 characters)
        words = [word for word in product_name.lower().split() if len(word) > 2]
        keywords.extend(words)
    
    if brand:
        keywords.append(brand.lower())
    
    if category:
        keywords.append(category.lower())
    
    # Remove duplicates while preserving order
    return list(dict.fromkeys(keywords))


def detect_availability_from_text(text: str) -> str:
    """Detect product availability from text."""
    if not text:
        return "unknown"
    
    text_lower = text.lower()
    
    # Out of stock indicators
    out_of_stock_patterns = [
        r'sin\s*stock',
        r'agotado',
        r'no\s*disponible',
        r'fuera\s*de\s*stock',
        r'temporalmente\s*no\s*disponible',
    ]
    
    for pattern in out_of_stock_patterns:
        if re.search(pattern, text_lower):
            return "out_of_stock"
    
    # Limited stock indicators
    limited_stock_patterns = [
        r'pocas\s*unidades',
        r'stock\s*limitado',
        r'últimas\s*unidades',
    ]
    
    for pattern in limited_stock_patterns:
        if re.search(pattern, text_lower):
            return "limited_stock"
    
    # In stock indicators
    in_stock_patterns = [
        r'disponible',
        r'en\s*stock',
        r'agregar\s*al\s*carrito',
        r'comprar',
    ]
    
    for pattern in in_stock_patterns:
        if re.search(pattern, text_lower):
            return "in_stock"
    
    return "unknown"


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove extra spaces and dots
    filename = re.sub(r'\.+', '.', filename)
    filename = re.sub(r'\s+', '_', filename)
    
    # Limit length
    if len(filename) > 200:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:200-len(ext)-1] + ('.' + ext if ext else '')
    
    return filename.strip('._')


def parse_date_string(date_str: str) -> Optional[datetime]:
    """Parse date string in various formats."""
    if not date_str:
        return None
    
    # Common date formats
    formats = [
        '%Y-%m-%d',
        '%d/%m/%Y',
        '%d-%m-%Y',
        '%Y-%m-%d %H:%M:%S',
        '%d/%m/%Y %H:%M:%S',
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str.strip(), fmt)
        except ValueError:
            continue
    
    return None
