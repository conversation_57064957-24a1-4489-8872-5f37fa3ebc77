"""
Vea scraper implementation.
Scraper for vea.com.ar - VTEX-based platform.
"""

import logging
from typing import Optional
from data.models import MarketEnum
from .vtex_scraper import VTEXScraper

logger = logging.getLogger(__name__)


class VeaScraper(VTEXScraper):
    """Scraper for Vea (vea.com.ar)."""
    
    def __init__(self, config):
        super().__init__(MarketEnum.VEA, config)
        self.base_url = "https://www.vea.com.ar"
        
        # Category mappings based on site structure
        self.categories = {
            "almacen": "Almacén",
            "bebidas": "Bebidas",
            "carnes-y-pescados": "Carnes y Pescados",
            "lacteos-y-refrigerados": "Lácteos y Refrigerados",
            "panaderia-y-reposteria": "Panadería y Repostería",
            "frutas-y-verduras": "Frutas y Verduras",
            "congelados": "Congelados",
            "limpieza": "Limpieza",
            "perfumeria-y-cuidado-personal": "Perfumería y Cuidado Personal",
            "electrohogar": "Electrohogar",
            "tecnologia": "Tecnología",
            "hogar-y-deco": "Hogar y Decoración",
            "deportes-y-fitness": "Deportes y Fitness",
            "juguetes": "Juguetes",
            "mascotas": "Mascotas"
        }
        
        # Vea-specific selectors (similar to other VTEX sites)
        self.selectors.update({
            'product_links': [
                'a[href*="/p/"]',
                '.vtex-product-summary-2-x-clearLink',
                '.shelf-item a'
            ],
            'product_name': [
                '.vtex-store-components-3-x-productBrand',
                '.vtex-product-summary-2-x-productBrand',
                '.product-name',
                'h1.product-title'
            ],
            'current_price': [
                '.vtex-product-price-1-x-sellingPrice',
                '.price-current',
                '.selling-price'
            ],
            'brand': [
                '.vtex-product-summary-2-x-productBrandName',
                '.product-brand'
            ]
        })
    
    def _get_category_url(self, category: str) -> str:
        """Get category URL for Vea."""
        return f"{self.base_url}/{category}"
    
    def _extract_product_id(self, url: str) -> Optional[str]:
        """Extract product ID from Vea URL."""
        try:
            if '/p/' in url:
                parts = url.split('/p/')
                if len(parts) > 1:
                    return parts[1].split('/')[0]
        except Exception:
            pass
        
        return url
