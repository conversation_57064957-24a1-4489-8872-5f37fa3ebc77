"""
Base VTEX scraper for sites using VTEX platform (Jumbo, Disco, Vea).
These sites share similar structure and can use common extraction logic.
"""

import logging
import time
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin
from abc import abstractmethod

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

from data.models import Product, MarketEnum, PriceInfo, ProductPromotion, ProductAvailability
from .base_scraper import BaseScraper
from .utils import (
    clean_text, extract_price_from_text, extract_brand_from_name,
    normalize_category, extract_weight_or_size, generate_search_keywords,
    detect_availability_from_text, extract_promotion_info, build_absolute_url
)

logger = logging.getLogger(__name__)


class VTEXScraper(BaseScraper):
    """Base scraper for VTEX-based supermarket sites."""
    
    def __init__(self, market: MarketEnum, config):
        super().__init__(market, config)
        
        # Common VTEX selectors - can be overridden by subclasses
        self.selectors = {
            'product_links': [
                'a[href*="/p/"]',
                '.vtex-product-summary-2-x-clearLink',
                '.shelf-item a'
            ],
            'product_name': [
                '.vtex-store-components-3-x-productBrand',
                '.product-name',
                'h1.product-title'
            ],
            'current_price': [
                '.vtex-product-price-1-x-sellingPrice',
                '.price-current',
                '.selling-price'
            ],
            'previous_price': [
                '.vtex-product-price-1-x-listPrice',
                '.price-before',
                '.list-price'
            ],
            'brand': [
                '.vtex-product-summary-2-x-productBrandName',
                '.product-brand'
            ],
            'category_breadcrumb': [
                '.breadcrumb a',
                '.vtex-breadcrumb-1-x-link'
            ],
            'image': [
                '.vtex-store-components-3-x-productImageTag',
                '.product-image img'
            ],
            'description': [
                '.product-description',
                '.vtex-product-summary-2-x-productDescription'
            ],
            'promotions': [
                '.promotion-badge',
                '.offer-label',
                '.vtex-product-summary-2-x-promotionBadge'
            ],
            'next_page': [
                '.pagination-next',
                'a[aria-label="Next"]',
                '.next-page'
            ]
        }
    
    @abstractmethod
    def _get_category_url(self, category: str) -> str:
        """Get category URL - must be implemented by subclasses."""
        pass
    
    def get_product_urls(self, category: Optional[str] = None) -> List[str]:
        """Get product URLs from category pages."""
        urls = []
        
        try:
            if category and category in self.categories:
                category_url = self._get_category_url(category)
                urls.extend(self._get_urls_from_category(category_url))
            else:
                # Get URLs from all categories
                for cat_key in self.categories.keys():
                    category_url = self._get_category_url(cat_key)
                    urls.extend(self._get_urls_from_category(category_url))
        
        except Exception as e:
            logger.error(f"Error getting product URLs: {e}")
        
        return list(set(urls))
    
    def _get_urls_from_category(self, category_url: str) -> List[str]:
        """Get product URLs from a specific category page."""
        urls = []
        
        try:
            # Try with requests first
            response = self._make_request(category_url)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')
                urls.extend(self._extract_urls_from_soup(soup))
                
                # Handle pagination
                urls.extend(self._handle_pagination_requests(category_url))
            
            # Fall back to Selenium if needed
            if not urls:
                urls.extend(self._get_urls_with_selenium(category_url))
        
        except Exception as e:
            logger.error(f"Error getting URLs from category {category_url}: {e}")
        
        return urls
    
    def _extract_urls_from_soup(self, soup: BeautifulSoup) -> List[str]:
        """Extract product URLs from BeautifulSoup object."""
        urls = []
        
        for selector in self.selectors['product_links']:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    full_url = build_absolute_url(self.base_url, href)
                    if '/p/' in full_url:
                        urls.append(full_url)
        
        return urls
    
    def _handle_pagination_requests(self, base_url: str) -> List[str]:
        """Handle pagination using requests."""
        urls = []
        page = 2
        max_pages = 10
        
        while page <= max_pages:
            try:
                page_url = f"{base_url}?page={page}"
                response = self._make_request(page_url)
                if not response:
                    break
                
                soup = BeautifulSoup(response.content, 'html.parser')
                page_urls = self._extract_urls_from_soup(soup)
                
                if page_urls:
                    urls.extend(page_urls)
                    page += 1
                else:
                    break
                    
            except Exception as e:
                logger.warning(f"Error handling pagination on page {page}: {e}")
                break
        
        return urls
    
    def _get_urls_with_selenium(self, category_url: str) -> List[str]:
        """Get URLs using Selenium as fallback."""
        urls = []
        
        try:
            if not self.driver:
                self.driver = self._setup_driver()
            
            if self._navigate_with_driver(category_url):
                urls.extend(self._extract_product_urls_selenium())
                urls.extend(self._handle_pagination_selenium())
        
        except Exception as e:
            logger.error(f"Error getting URLs with Selenium: {e}")
        
        return urls
    
    def _extract_product_urls_selenium(self) -> List[str]:
        """Extract product URLs using Selenium."""
        urls = []
        
        try:
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "a"))
            )
            
            for selector in self.selectors['product_links']:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and '/p/' in href:
                            urls.append(href)
                    
                    if urls:
                        break
                        
                except NoSuchElementException:
                    continue
        
        except TimeoutException:
            logger.warning("Timeout waiting for products to load")
        
        return urls
    
    def _handle_pagination_selenium(self) -> List[str]:
        """Handle pagination using Selenium."""
        urls = []
        page = 2
        max_pages = 5
        
        while page <= max_pages:
            try:
                next_button = None
                for selector in self.selectors['next_page']:
                    try:
                        next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if next_button.is_enabled():
                            break
                    except NoSuchElementException:
                        continue
                
                if next_button and next_button.is_enabled():
                    self.driver.execute_script("arguments[0].click();", next_button)
                    time.sleep(3)
                    
                    page_urls = self._extract_product_urls_selenium()
                    if page_urls:
                        urls.extend(page_urls)
                        page += 1
                    else:
                        break
                else:
                    break
                    
            except Exception as e:
                logger.warning(f"Error handling pagination on page {page}: {e}")
                break
        
        return urls
    
    def extract_product_data(self, url: str) -> Optional[Product]:
        """Extract product data from a specific product URL."""
        try:
            # Try with requests first
            response = self._make_request(url)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')
                product = self._extract_from_soup(soup, url)
                if product:
                    return product
            
            # Fall back to Selenium
            return self._extract_with_selenium(url)
            
        except Exception as e:
            logger.error(f"Error extracting product data from {url}: {e}")
            return None
    
    def _extract_from_soup(self, soup: BeautifulSoup, url: str) -> Optional[Product]:
        """Extract product data from BeautifulSoup object."""
        try:
            # Extract product name
            name = self._extract_text_by_selectors(soup, self.selectors['product_name'])
            if not name:
                return None
            
            # Extract price
            price_info = self._extract_price_from_soup(soup)
            if not price_info:
                return None
            
            # Extract other details
            brand = self._extract_text_by_selectors(soup, self.selectors['brand'])
            if not brand:
                brand = extract_brand_from_name(name)
            
            category = self._extract_category_from_soup(soup)
            image_url = self._extract_image_from_soup(soup)
            description = self._extract_text_by_selectors(soup, self.selectors['description'])
            availability = self._extract_availability_from_soup(soup)
            promotions = self._extract_promotions_from_soup(soup)
            weight = extract_weight_or_size(f"{name} {description or ''}")
            
            keywords = generate_search_keywords(name, brand, category)
            
            product = Product(
                name=clean_text(name),
                brand=brand,
                category=normalize_category(category) if category else None,
                price=price_info,
                market=self.market,
                market_product_id=self._extract_product_id(url),
                url=url,
                image_url=image_url,
                availability=availability,
                promotions=promotions,
                description=clean_text(description) if description else None,
                weight=weight,
                search_keywords=keywords
            )
            
            return product
            
        except Exception as e:
            logger.error(f"Error extracting from soup: {e}")
            return None
    
    def _extract_text_by_selectors(self, soup: BeautifulSoup, selectors: List[str]) -> Optional[str]:
        """Extract text using multiple selectors."""
        for selector in selectors:
            element = soup.select_one(selector)
            if element and element.get_text(strip=True):
                return element.get_text(strip=True)
        return None
    
    def _extract_price_from_soup(self, soup: BeautifulSoup) -> Optional[PriceInfo]:
        """Extract price information from soup."""
        try:
            # Current price
            current_price = None
            for selector in self.selectors['current_price']:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    current_price = extract_price_from_text(price_text)
                    if current_price:
                        break
            
            if not current_price:
                return None
            
            # Previous price
            previous_price = None
            for selector in self.selectors['previous_price']:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    previous_price = extract_price_from_text(price_text)
                    if previous_price:
                        break
            
            # Calculate discount
            discount_percentage = None
            if previous_price and previous_price > current_price:
                discount_percentage = round(((previous_price - current_price) / previous_price) * 100, 2)
            
            return PriceInfo(
                current=current_price,
                previous=previous_price,
                currency="ARS",
                discount_percentage=discount_percentage
            )
            
        except Exception as e:
            logger.error(f"Error extracting price: {e}")
            return None
    
    def _extract_category_from_soup(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract category from breadcrumbs."""
        for selector in self.selectors['category_breadcrumb']:
            elements = soup.select(selector)
            if len(elements) > 1:
                category = elements[1].get_text(strip=True)
                if category and category.lower() != "home":
                    return category
        return None
    
    def _extract_image_from_soup(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract product image URL."""
        for selector in self.selectors['image']:
            element = soup.select_one(selector)
            if element:
                src = element.get('src') or element.get('data-src')
                if src:
                    return build_absolute_url(self.base_url, src)
        return None
    
    def _extract_availability_from_soup(self, soup: BeautifulSoup) -> ProductAvailability:
        """Extract availability status."""
        try:
            page_text = soup.get_text().lower()
            availability_text = detect_availability_from_text(page_text)
            
            if availability_text == "out_of_stock":
                return ProductAvailability.OUT_OF_STOCK
            elif availability_text == "limited_stock":
                return ProductAvailability.LIMITED_STOCK
            elif availability_text == "in_stock":
                return ProductAvailability.IN_STOCK
                
        except Exception as e:
            logger.warning(f"Error extracting availability: {e}")
        
        return ProductAvailability.UNKNOWN
    
    def _extract_promotions_from_soup(self, soup: BeautifulSoup) -> List[ProductPromotion]:
        """Extract promotions from soup."""
        promotions = []
        
        try:
            for selector in self.selectors['promotions']:
                elements = soup.select(selector)
                for element in elements:
                    promo_text = element.get_text(strip=True)
                    if promo_text:
                        promo_info = extract_promotion_info(promo_text)
                        if promo_info:
                            promotions.append(ProductPromotion(
                                type=promo_info['type'],
                                description=promo_info['description']
                            ))
        
        except Exception as e:
            logger.warning(f"Error extracting promotions: {e}")
        
        return promotions
    
    def _extract_with_selenium(self, url: str) -> Optional[Product]:
        """Extract product data using Selenium as fallback."""
        # This would implement Selenium-based extraction
        # For now, return None to keep the implementation focused
        return None
    
    def _extract_product_id(self, url: str) -> Optional[str]:
        """Extract product ID from URL - should be overridden by subclasses."""
        try:
            if '/p/' in url:
                parts = url.split('/p/')
                if len(parts) > 1:
                    return parts[1].split('/')[0]
        except Exception:
            pass
        
        return url
    
    async def scrape_products(self, categories: Optional[List[str]] = None) -> List[Product]:
        """Scrape products from the VTEX-based site."""
        products = []
        
        try:
            # Get product URLs
            if categories:
                all_urls = []
                for category in categories:
                    if category in self.categories:
                        urls = self.get_product_urls(category)
                        all_urls.extend(urls)
            else:
                all_urls = self.get_product_urls()
            
            logger.info(f"Found {len(all_urls)} product URLs to scrape")
            
            # Extract product data
            for i, url in enumerate(all_urls):
                if url in self.scraped_urls:
                    continue
                
                logger.info(f"Scraping product {i+1}/{len(all_urls)}: {url}")
                
                product = self.extract_product_data(url)
                if product:
                    products.append(product)
                    self.scraped_urls.add(url)
                
                # Update session stats
                if self.scraping_session:
                    await self._update_scraping_session(
                        products_found=len(products)
                    )
                
                time.sleep(self.config.rate_limit_delay)
        
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
        
        return products
