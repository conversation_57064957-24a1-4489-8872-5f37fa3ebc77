#!/usr/bin/env python3
"""
Health check script for the supermarket scraper application.
"""

import requests
import sys
import time
import json
from typing import Dict, Any

def check_api_health(base_url: str = "http://localhost:8000") -> bool:
    """Check API health."""
    try:
        print(f"🔍 Checking API health at {base_url}...")
        
        # Basic health check
        response = requests.get(f"{base_url}/api/v1/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API is healthy")
            print(f"   Database: {health_data.get('database', 'unknown')}")
            print(f"   Total products: {health_data.get('total_products', 0)}")
            
            # Detailed health check
            response = requests.get(f"{base_url}/api/v1/health/detailed", timeout=10)
            if response.status_code == 200:
                detailed_data = response.json()
                print(f"✅ Detailed health check passed")
                if 'database' in detailed_data:
                    db_info = detailed_data['database']
                    print(f"   Products by market: {db_info.get('products_by_market', {})}")
            
            return True
        else:
            print(f"❌ API health check failed: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to API at {base_url}")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ API health check timed out")
        return False
    except Exception as e:
        print(f"❌ API health check error: {e}")
        return False

def check_api_endpoints(base_url: str = "http://localhost:8000") -> bool:
    """Check various API endpoints."""
    endpoints = [
        ("/api/v1/markets", "Markets endpoint"),
        ("/api/v1/products?limit=1", "Products endpoint"),
        ("/api/v1/products/categories", "Categories endpoint"),
        ("/", "Root endpoint")
    ]
    
    print("🔍 Checking API endpoints...")
    
    all_passed = True
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {description}: OK")
            else:
                print(f"❌ {description}: HTTP {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"❌ {description}: {e}")
            all_passed = False
    
    return all_passed

def check_docker_services() -> bool:
    """Check Docker services status."""
    import subprocess
    
    try:
        print("🐳 Checking Docker services...")
        
        # Check if docker-compose is running
        result = subprocess.run(
            ["docker-compose", "ps"], 
            capture_output=True, 
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Docker Compose services:")
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # Skip header
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print(f"❌ Docker Compose check failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Docker Compose check timed out")
        return False
    except FileNotFoundError:
        print("⚠️  Docker Compose not found (this is OK if not using Docker)")
        return True
    except Exception as e:
        print(f"❌ Docker services check error: {e}")
        return False

def wait_for_api(base_url: str = "http://localhost:8000", max_wait: int = 60) -> bool:
    """Wait for API to become available."""
    print(f"⏳ Waiting for API to become available (max {max_wait}s)...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{base_url}/api/v1/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ API is now available")
                return True
        except:
            pass
        
        time.sleep(2)
        print(".", end="", flush=True)
    
    print(f"\n❌ API did not become available within {max_wait} seconds")
    return False

def main():
    """Main health check function."""
    print("🏥 Supermarket Scraper Health Check")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Check if we should wait for API
    if len(sys.argv) > 1 and sys.argv[1] == "--wait":
        if not wait_for_api(base_url):
            sys.exit(1)
    
    checks_passed = 0
    total_checks = 3
    
    # Check Docker services
    if check_docker_services():
        checks_passed += 1
    
    # Check API health
    if check_api_health(base_url):
        checks_passed += 1
    
    # Check API endpoints
    if check_api_endpoints(base_url):
        checks_passed += 1
    
    print("=" * 40)
    print(f"📊 Health Check Results: {checks_passed}/{total_checks} checks passed")
    
    if checks_passed == total_checks:
        print("✅ All health checks passed! System is operational.")
        
        # Show useful URLs
        print("\n📚 Useful URLs:")
        print(f"   API Documentation: {base_url}/docs")
        print(f"   Health Check: {base_url}/api/v1/health")
        print(f"   Markets: {base_url}/api/v1/markets")
        
        sys.exit(0)
    else:
        print("❌ Some health checks failed. Please check the system.")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Health check interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error during health check: {e}")
        sys.exit(1)
