#!/usr/bin/env python3
"""
<PERSON>ript to run the scraper manually.
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.models import ScrapingConfig, MarketEnum
from scrapers.scraper_manager import ScraperManager
from config import settings


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run supermarket scraper")
    parser.add_argument(
        "--market",
        choices=[market.value for market in MarketEnum],
        help="Scrape specific market only"
    )
    parser.add_argument(
        "--categories",
        nargs="+",
        help="Scrape specific categories only"
    )
    parser.add_argument(
        "--headless",
        action="store_true",
        default=settings.headless_browser,
        help="Run browser in headless mode"
    )
    parser.add_argument(
        "--rate-limit",
        type=float,
        default=settings.rate_limit_delay,
        help="Rate limit delay in seconds"
    )
    
    args = parser.parse_args()
    
    # Create configuration
    config = ScrapingConfig(
        rate_limit_delay=args.rate_limit,
        max_retries=settings.max_retries,
        timeout=settings.timeout,
        headless=args.headless,
        user_agent_rotation=settings.user_agent_rotation,
        markets_enabled=settings.enabled_markets
    )
    
    # Create scraper manager
    manager = ScraperManager(config)
    
    try:
        if args.market:
            # Scrape single market
            market = MarketEnum(args.market)
            print(f"Scraping {market.value}...")
            count = await manager.scrape_single_market(market, args.categories)
            print(f"Scraped {count} products from {market.value}")
        else:
            # Scrape all markets
            print("Scraping all enabled markets...")
            results = await manager.scrape_all_markets(args.categories)
            
            total = sum(results.values())
            print(f"Scraping completed. Total products: {total}")
            
            for market, count in results.items():
                print(f"  {market}: {count} products")
    
    except KeyboardInterrupt:
        print("\nScraping interrupted by user")
    except Exception as e:
        print(f"Error during scraping: {e}")
        sys.exit(1)
    finally:
        manager.cleanup_all_scrapers()


if __name__ == "__main__":
    asyncio.run(main())
