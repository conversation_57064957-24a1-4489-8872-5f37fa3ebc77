#!/usr/bin/env python3
"""
Script to setup and initialize the database.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.database import db_manager
from config import settings


async def main():
    """Setup database."""
    print("Setting up database...")
    
    try:
        # Connect to database
        await db_manager.connect()
        print("✓ Connected to MongoDB")
        
        # Database is automatically initialized with indexes when connecting
        print("✓ Database indexes created")
        
        # Test database operations
        stats = await db_manager.get_product_stats()
        print(f"✓ Database operational (current products: {stats.total_products})")
        
        print("\nDatabase setup completed successfully!")
        
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        sys.exit(1)
    
    finally:
        await db_manager.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
