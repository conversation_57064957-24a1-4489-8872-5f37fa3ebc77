#!/usr/bin/env python3
"""
Script to test the Docker build and basic functionality.
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            timeout=300
        )
        if result.returncode != 0:
            print(f"Error: {result.stderr}")
            return False, result.stderr
        return True, result.stdout
    except subprocess.TimeoutExpired:
        print("Command timed out")
        return False, "Timeout"
    except Exception as e:
        print(f"Exception: {e}")
        return False, str(e)

def test_docker_build():
    """Test Docker build process."""
    print("🔨 Testing Docker build...")
    
    project_root = Path(__file__).parent.parent
    
    # Build API image
    print("Building API image...")
    success, output = run_command(
        "docker build -f docker/api/Dockerfile -t supermarket-api .", 
        cwd=project_root
    )
    if not success:
        print("❌ API build failed")
        return False
    print("✅ API build successful")
    
    # Build scraper image
    print("Building scraper image...")
    success, output = run_command(
        "docker build -f docker/scraper/Dockerfile -t supermarket-scraper .", 
        cwd=project_root
    )
    if not success:
        print("❌ Scraper build failed")
        return False
    print("✅ Scraper build successful")
    
    return True

def test_docker_compose():
    """Test Docker Compose startup."""
    print("🚀 Testing Docker Compose...")
    
    project_root = Path(__file__).parent.parent
    
    # Start services
    print("Starting services...")
    success, output = run_command(
        "docker-compose up -d", 
        cwd=project_root
    )
    if not success:
        print("❌ Docker Compose startup failed")
        return False
    
    # Wait for services to start
    print("Waiting for services to start...")
    time.sleep(30)
    
    # Check if API is responding
    print("Testing API health...")
    try:
        response = requests.get("http://localhost:8000/api/v1/health", timeout=10)
        if response.status_code == 200:
            print("✅ API is responding")
            print(f"Health check response: {response.json()}")
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check failed: {e}")
        return False
    
    # Check service status
    success, output = run_command(
        "docker-compose ps", 
        cwd=project_root
    )
    if success:
        print("Service status:")
        print(output)
    
    return True

def cleanup():
    """Clean up Docker resources."""
    print("🧹 Cleaning up...")
    
    project_root = Path(__file__).parent.parent
    
    # Stop services
    run_command("docker-compose down", cwd=project_root)
    
    # Remove images
    run_command("docker rmi supermarket-api supermarket-scraper", cwd=project_root)
    
    print("✅ Cleanup complete")

def main():
    """Main test function."""
    print("🧪 Starting Docker build and functionality tests...")
    
    try:
        # Test Docker build
        if not test_docker_build():
            print("❌ Docker build tests failed")
            sys.exit(1)
        
        # Test Docker Compose
        if not test_docker_compose():
            print("❌ Docker Compose tests failed")
            cleanup()
            sys.exit(1)
        
        print("✅ All tests passed!")
        
        # Show API documentation URL
        print("\n📚 API Documentation available at:")
        print("   http://localhost:8000/docs")
        print("\n🔍 To test manually:")
        print("   curl http://localhost:8000/api/v1/health")
        print("   curl http://localhost:8000/api/v1/markets")
        
        # Ask if user wants to keep services running
        keep_running = input("\n🤔 Keep services running? (y/N): ").lower().strip()
        if keep_running != 'y':
            cleanup()
        else:
            print("Services are still running. Use 'docker-compose down' to stop them.")
    
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
