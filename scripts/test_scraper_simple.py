#!/usr/bin/env python3
"""
Simple test script to verify scraper functionality without Selenium.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from data.models import ScrapingConfig, MarketEnum, Product, PriceInfo
    from data.database import db_manager
    from scrapers.utils import (
        extract_price_from_text,
        extract_brand_from_name,
        normalize_category,
        extract_weight_or_size,
        detect_availability_from_text
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


async def test_database_connection():
    """Test database connection."""
    print("🔌 Testing database connection...")
    
    try:
        await db_manager.connect()
        print("✅ Database connection successful")
        
        # Test basic operations
        stats = await db_manager.get_product_stats()
        print(f"✅ Database stats retrieved: {stats.total_products} products")
        
        await db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def test_utility_functions():
    """Test utility functions."""
    print("🔧 Testing utility functions...")
    
    # Test price extraction
    test_prices = [
        ("$1.234,56", 1234.56),
        ("1.234,56 $", 1234.56),
        ("$1234", 1234.0),
        ("1234,56 pesos", 1234.56),
        ("No price", None)
    ]
    
    for price_text, expected in test_prices:
        result = extract_price_from_text(price_text)
        if result == expected:
            print(f"✅ Price extraction: '{price_text}' -> {result}")
        else:
            print(f"❌ Price extraction failed: '{price_text}' -> {result} (expected {expected})")
            return False
    
    # Test brand extraction
    test_brands = [
        ("COCA COLA Gaseosa 2.25L", "COCA"),
        ("Quilmes Cerveza 1L", "Quilmes"),
        ("simple product", None)
    ]
    
    for product_name, expected in test_brands:
        result = extract_brand_from_name(product_name)
        if result == expected:
            print(f"✅ Brand extraction: '{product_name}' -> {result}")
        else:
            print(f"❌ Brand extraction failed: '{product_name}' -> {result} (expected {expected})")
    
    # Test category normalization
    test_categories = [
        ("lacteos", "Lácteos"),
        ("BEBIDAS", "Bebidas"),
        ("limpieza", "Limpieza")
    ]
    
    for category, expected in test_categories:
        result = normalize_category(category)
        if result == expected:
            print(f"✅ Category normalization: '{category}' -> {result}")
        else:
            print(f"❌ Category normalization failed: '{category}' -> {result} (expected {expected})")
    
    print("✅ Utility functions test completed")
    return True


def test_data_models():
    """Test data models."""
    print("📊 Testing data models...")
    
    try:
        # Test PriceInfo model
        price_info = PriceInfo(
            current=1234.56,
            previous=1500.0,
            currency="ARS",
            discount_percentage=17.7
        )
        print(f"✅ PriceInfo model: {price_info.current} ARS")
        
        # Test Product model
        product = Product(
            name="Test Product",
            price=price_info,
            market=MarketEnum.COTO,
            url="https://test.com/product/1"
        )
        print(f"✅ Product model: {product.name} from {product.market}")
        
        # Test ScrapingConfig model
        config = ScrapingConfig(
            rate_limit_delay=2.0,
            max_retries=3,
            timeout=30,
            headless=True,
            markets_enabled=[MarketEnum.COTO, MarketEnum.CARREFOUR]
        )
        print(f"✅ ScrapingConfig model: {len(config.markets_enabled)} markets enabled")
        
        return True
        
    except Exception as e:
        print(f"❌ Data models test failed: {e}")
        return False


async def test_database_operations():
    """Test database operations."""
    print("💾 Testing database operations...")
    
    try:
        await db_manager.connect()
        
        # Create test product
        test_product = Product(
            name="Test Product for Database",
            price=PriceInfo(current=99.99),
            market=MarketEnum.COTO,
            market_product_id="test_123",
            url="https://test.com/product/test_123"
        )
        
        # Save product
        success = await db_manager.save_product(test_product)
        if success:
            print("✅ Product saved successfully")
        else:
            print("❌ Product save failed")
            return False
        
        # Search for product
        from data.models import SearchQuery
        search_query = SearchQuery(
            query="Test Product",
            limit=1
        )
        
        results = await db_manager.search_products(search_query)
        if results:
            print(f"✅ Product search successful: found {len(results)} results")
        else:
            print("⚠️  Product search returned no results (this might be expected)")
        
        await db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🧪 Starting simple scraper functionality tests...")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test utility functions
    if test_utility_functions():
        tests_passed += 1
    
    # Test data models
    if test_data_models():
        tests_passed += 1
    
    # Test database connection
    if await test_database_connection():
        tests_passed += 1
    
    # Test database operations
    if await test_database_operations():
        tests_passed += 1
    
    print("=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! The scraper components are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
