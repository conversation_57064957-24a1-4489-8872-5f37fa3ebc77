#!/usr/bin/env python3
"""
Simple Enhanced Scraper Test & Monitoring Demo
==============================================

This script demonstrates the key enhanced features in a working way.
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

# Import our enhanced scraper components
import sys
sys.path.append('/app')

from scrapers.advanced_utils import (
    get_metrics_collector, PerformanceMonitor, 
    retry_page_load, retry_element_find
)

class SimpleScraperDemo:
    """Simple demonstration of enhanced scraper capabilities."""
    
    def __init__(self):
        self.metrics = get_metrics_collector()
        self.driver = None
        
    def setup_browser(self):
        """Setup Chrome browser."""
        print("🔧 Setting up Chrome browser...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome browser ready!")
            return True
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def test_monitored_scraping(self):
        """Test scraping with performance monitoring."""
        print("\n📊 Testing Monitored Scraping...")
        
        test_sites = [
            ("Example.com", "https://example.com"),
            ("HTTPBin HTML", "https://httpbin.org/html"),
            ("HTTPBin JSON", "https://httpbin.org/json")
        ]
        
        for name, url in test_sites:
            print(f"\n🌐 Scraping {name}...")
            
            # Use performance monitoring
            with PerformanceMonitor(self.metrics, "page_scraping", url=url):
                try:
                    # Load page with retry logic
                    @retry_page_load
                    def load_page():
                        self.driver.get(url)
                        return self.driver.title
                    
                    title = load_page()
                    print(f"   ✅ Title: {title}")
                    
                    # Try to find elements with retry
                    @retry_element_find  
                    def find_elements():
                        return self.driver.find_elements(By.TAG_NAME, "p")
                    
                    paragraphs = find_elements()
                    print(f"   📄 Found {len(paragraphs)} paragraphs")
                    
                    # Simulate some processing time
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"   ❌ Error: {e}")
    
    def test_real_ecommerce_site(self):
        """Test with a real e-commerce site."""
        print("\n🛒 Testing Real E-commerce Site...")
        
        url = "https://www.mercadolibre.com.ar"
        
        with PerformanceMonitor(self.metrics, "ecommerce_scraping", url=url):
            try:
                print(f"🌐 Loading MercadoLibre...")
                
                @retry_page_load
                def load_mercadolibre():
                    self.driver.get(url)
                    # Wait for page to load
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    return self.driver.title
                
                title = load_mercadolibre()
                print(f"   📄 Page loaded: {title}")
                
                # Try to find search elements
                @retry_element_find
                def find_search_elements():
                    search_boxes = self.driver.find_elements(By.CSS_SELECTOR, 
                        "input[type='search'], input[placeholder*='Buscar'], .nav-search-input")
                    return search_boxes
                
                search_elements = find_search_elements()
                print(f"   🔍 Found {len(search_elements)} search elements")
                
                # Look for product categories or links
                categories = self.driver.find_elements(By.CSS_SELECTOR, 
                    "a[href*='categoria'], a[href*='category'], .category")
                print(f"   📂 Found {len(categories)} category links")
                
                print("   ✅ E-commerce site scraping successful!")
                
            except Exception as e:
                print(f"   ❌ E-commerce scraping failed: {e}")
    
    def show_performance_metrics(self):
        """Display performance metrics dashboard."""
        print("\n📈 PERFORMANCE METRICS DASHBOARD")
        print("=" * 50)
        
        # Get performance summary
        summary = self.metrics.get_performance_summary()
        
        print(f"⏱️  System Uptime: {summary['uptime_seconds']:.1f} seconds")
        print(f"🔢 Total Operations: {summary['total_operations']}")
        print(f"✅ Overall Success Rate: {summary['overall_success_rate']:.1%}")
        
        if summary['operations']:
            print("\n📋 Detailed Operation Metrics:")
            print("-" * 40)
            
            for operation, stats in summary['operations'].items():
                print(f"\n🔹 {operation.replace('_', ' ').title()}:")
                print(f"   • Total Runs: {stats['total']}")
                print(f"   • Success Rate: {stats['success_rate']:.1%}")
                print(f"   • Average Time: {stats['avg_duration']:.2f}s")
                print(f"   • 95th Percentile: {stats['p95_duration']:.2f}s")
        
        # Show any recent failures
        print("\n🚨 Recent Issues (if any):")
        print("-" * 30)
        
        has_failures = False
        for operation in summary['operations'].keys():
            failures = self.metrics.get_recent_failures(operation, limit=2)
            if failures:
                has_failures = True
                print(f"\n❌ {operation}:")
                for failure in failures:
                    error_msg = failure.error[:60] + "..." if len(failure.error) > 60 else failure.error
                    print(f"   • {error_msg}")
        
        if not has_failures:
            print("   🎉 No recent failures - system running smoothly!")
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            self.driver.quit()
            print("\n🧹 Cleanup completed!")

def main():
    """Main test function."""
    print("🚀 ENHANCED SCRAPER SYSTEM - LIVE DEMO")
    print("=" * 50)
    
    demo = SimpleScraperDemo()
    
    try:
        # Setup browser
        if not demo.setup_browser():
            print("❌ Cannot proceed without browser")
            return
        
        # Run tests
        demo.test_monitored_scraping()
        demo.test_real_ecommerce_site()
        
        # Show comprehensive metrics
        demo.show_performance_metrics()
        
        print("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("💡 Enhanced features working perfectly:")
        print("   ✅ Performance monitoring active")
        print("   ✅ Retry logic functioning") 
        print("   ✅ Real-time metrics collection")
        print("   ✅ Error handling improved")
        
    except Exception as e:
        print(f"\n💥 Demo failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        demo.cleanup()

if __name__ == "__main__":
    main()
