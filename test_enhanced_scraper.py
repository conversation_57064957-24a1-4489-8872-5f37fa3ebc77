#!/usr/bin/env python3
"""
Enhanced Scraper System Test & Monitoring Demonstration
========================================================

This script demonstrates all the new features:
1. Advanced retry logic with smart backoff
2. Circuit breaker pattern for fault tolerance
3. Real-time performance monitoring
4. Comprehensive error handling
5. Live metrics collection and analysis
"""

import time
import json
import random
import asyncio
from typing import Dict, Any
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Import our enhanced scraper components
import sys
sys.path.append('/app')

from scrapers.advanced_utils import (
    get_metrics_collector, PerformanceMonitor, 
    ScrapingCircuitBreaker, SmartRetry, retry_page_load,
    get_retry_config
)

class EnhancedScraperDemo:
    """Demonstration of enhanced scraper capabilities."""
    
    def __init__(self):
        self.metrics = get_metrics_collector()
        self.circuit_breaker = ScrapingCircuitBreaker()
        self.retry_handler = SmartRetry()
        self.driver = None
        
    def setup_browser(self):
        """Setup Chrome browser with optimal settings."""
        print("🔧 Setting up Chrome browser...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome browser initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize browser: {e}")
            return False
    
    def test_basic_scraping(self):
        """Test basic scraping functionality with monitoring."""
        print("\n🌐 Testing Basic Scraping with Monitoring...")
        
        test_urls = [
            "https://httpbin.org/html",
            "https://httpbin.org/json", 
            "https://example.com"
        ]
        
        for url in test_urls:
            with PerformanceMonitor(self.metrics, "page_load", url=url):
                try:
                    print(f"📄 Loading: {url}")
                    self.driver.get(url)
                    time.sleep(1)  # Simulate processing time
                    
                    # Extract some basic info
                    title = self.driver.title
                    print(f"   ✅ Title: {title[:50]}...")
                    
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    raise
    
    def test_retry_logic(self):
        """Demonstrate retry logic with simulated failures."""
        print("\n🔄 Testing Retry Logic with Simulated Failures...")
        
        def flaky_operation():
            """Simulates a flaky operation that fails randomly."""
            if random.random() < 0.7:  # 70% failure rate
                raise TimeoutException("Simulated network timeout")
            return "Success!"
        
        # Test retry with different configurations
        retry_configs = [
            ("Conservative", get_retry_config('page_load')),
            ("Aggressive", get_retry_config('api_request')),
        ]

        for config_name, config in retry_configs:
            print(f"\n   🎯 Testing {config_name} Retry Strategy:")

            with PerformanceMonitor(self.metrics, f"retry_test_{config_name.lower()}"):
                try:
                    # Use the retry_with_backoff function directly
                    from scrapers.advanced_utils import retry_with_backoff
                    result = retry_with_backoff(flaky_operation, config)
                    print(f"      ✅ {result}")
                except Exception as e:
                    print(f"      ❌ Final failure: {e}")
    
    def test_circuit_breaker(self):
        """Demonstrate circuit breaker protection."""
        print("\n🛡️ Testing Circuit Breaker Protection...")
        
        def failing_service():
            """Simulates a consistently failing service."""
            raise WebDriverException("Service unavailable")
        
        print("   📊 Triggering multiple failures to open circuit breaker...")
        
        # Trigger failures to open the circuit breaker
        for i in range(8):
            with PerformanceMonitor(self.metrics, "circuit_breaker_test"):
                try:
                    self.circuit_breaker.call(failing_service)
                except Exception as e:
                    print(f"      Attempt {i+1}: {type(e).__name__}")
                    
                    # Check circuit breaker state
                    state = self.circuit_breaker.state.name
                    print(f"      Circuit Breaker State: {state}")
                    
                    if state == "OPEN":
                        print("      🚨 Circuit breaker is now OPEN - protecting system!")
                        break
    
    def test_real_website_scraping(self):
        """Test scraping a real website with full monitoring."""
        print("\n🏪 Testing Real Website Scraping (Carrefour Argentina)...")
        
        url = "https://www.carrefour.com.ar"
        
        with PerformanceMonitor(self.metrics, "real_website_scraping", url=url):
            try:
                print(f"🌐 Loading Carrefour homepage...")
                
                # Use retry logic for page load
                @retry_page_load
                def load_page():
                    self.driver.get(url)
                    return WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                
                load_page()
                
                # Extract basic information
                title = self.driver.title
                print(f"   📄 Page Title: {title}")
                
                # Try to find some products or categories
                try:
                    # Look for common e-commerce elements
                    elements = self.driver.find_elements(By.CSS_SELECTOR, 
                        "a[href*='product'], a[href*='categoria'], .product, .category")
                    print(f"   🛍️ Found {len(elements)} product/category elements")
                    
                    if elements:
                        for i, elem in enumerate(elements[:3]):
                            try:
                                text = elem.text.strip()
                                if text:
                                    print(f"      {i+1}. {text[:50]}...")
                            except:
                                pass
                                
                except Exception as e:
                    print(f"   ⚠️ Could not extract detailed info: {e}")
                
                print("   ✅ Real website scraping completed successfully!")
                
            except Exception as e:
                print(f"   ❌ Real website scraping failed: {e}")
                raise
    
    def display_metrics_dashboard(self):
        """Display comprehensive metrics dashboard."""
        print("\n📊 PERFORMANCE METRICS DASHBOARD")
        print("=" * 50)
        
        # Get overall performance summary
        summary = self.metrics.get_performance_summary()
        
        print(f"🕐 System Uptime: {summary['uptime_seconds']:.1f} seconds")
        print(f"📈 Total Operations: {summary['total_operations']}")
        print(f"✅ Overall Success Rate: {summary['overall_success_rate']:.1%}")
        
        print("\n📋 Operation Details:")
        print("-" * 30)
        
        for operation, stats in summary['operations'].items():
            print(f"\n🔹 {operation.replace('_', ' ').title()}:")
            print(f"   Total: {stats['total']}")
            print(f"   Success Rate: {stats['success_rate']:.1%}")
            print(f"   Avg Duration: {stats['avg_duration']:.2f}s")
            print(f"   95th Percentile: {stats['p95_duration']:.2f}s")
        
        # Show recent failures
        print("\n🚨 Recent Failures Analysis:")
        print("-" * 30)
        
        for operation in summary['operations'].keys():
            failures = self.metrics.get_recent_failures(operation, limit=3)
            if failures:
                print(f"\n❌ {operation}:")
                for failure in failures:
                    print(f"   • {failure.error} (Duration: {failure.duration:.2f}s)")
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            self.driver.quit()
            print("\n🧹 Browser cleaned up successfully!")

def main():
    """Main demonstration function."""
    print("🚀 ENHANCED SCRAPER SYSTEM DEMONSTRATION")
    print("=" * 60)
    
    demo = EnhancedScraperDemo()
    
    try:
        # Setup
        if not demo.setup_browser():
            return
        
        # Run all tests
        demo.test_basic_scraping()
        demo.test_retry_logic()
        demo.test_circuit_breaker()
        demo.test_real_website_scraping()
        
        # Show comprehensive metrics
        demo.display_metrics_dashboard()
        
        print("\n🎉 All tests completed successfully!")
        print("💡 The enhanced scraper system is working perfectly!")
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        demo.cleanup()

if __name__ == "__main__":
    main()
