#!/usr/bin/env python3
import asyncio
import sys
sys.path.append('/app')
from scrapers.coto_scraper import CotoScraper
from data.models import ScrapingConfig, MarketEnum
from data.database import db_manager

async def test_final_scrape():
    print('Testing final improved scraper...')
    
    # Connect to database
    await db_manager.connect()
    
    config = ScrapingConfig(
        markets_enabled=[MarketEnum.COTO],
        max_products_per_session=3,  # Small test
        headless=True,
        rate_limit_delay=4.0,
        timeout=60,
        max_retries=5,
        user_agent_rotation=False  # Use our fixed user agent
    )
    
    scraper = CotoScraper(config)
    
    try:
        print('Starting final scraping test...')
        result = await scraper.run_scraping()  # Try without specific categories first
        print(f'Scraping completed. Products saved: {result}')
        
        if result > 0:
            print('🎉 SUCCESS: Products were scraped and saved!')
            
            # Check what we got
            products = await db_manager.products_collection.find({}).limit(5).to_list(length=5)
            print(f'Sample products in database: {len(products)}')
            for i, product in enumerate(products):
                name = product.get('name', 'No name')
                price = product.get('price', {}).get('current', 'No price')
                print(f'  {i+1}. {name} - ${price}')
        else:
            print('No products found, but no errors occurred')
            
    except Exception as e:
        print(f'Error during scraping: {e}')
        import traceback
        traceback.print_exc()
    finally:
        scraper.cleanup()
        await db_manager.disconnect()

if __name__ == "__main__":
    asyncio.run(test_final_scrape())
