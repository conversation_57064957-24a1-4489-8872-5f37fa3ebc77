"""
Tests for API functionality.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from api.main import app
from data.models import ProductStats, MarketEnum


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_db_manager():
    """Mock database manager."""
    with patch('api.dependencies.db_manager') as mock:
        yield mock


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_health_check(self, client, mock_db_manager):
        """Test basic health check."""
        # Mock database ping
        mock_db_manager.client.admin.command = AsyncMock(return_value=True)
        mock_db_manager.products_collection.count_documents = AsyncMock(return_value=1234)
        
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["database"] == "connected"
    
    def test_detailed_health_check(self, client, mock_db_manager):
        """Test detailed health check."""
        # Mock database operations
        mock_db_manager.client.admin.command = AsyncMock(return_value=True)
        mock_db_manager.get_product_stats = AsyncMock(return_value=ProductStats(
            total_products=1234,
            products_by_market={"coto": 500, "carrefour": 734},
            products_by_category={"Bebidas": 200},
            average_price_by_market={"coto": 567.89},
            last_update="2024-01-01T00:00:00Z",
            products_with_promotions=123,
            out_of_stock_products=45
        ))
        mock_db_manager.products_collection.find = AsyncMock()
        mock_db_manager.products_collection.find.return_value.sort.return_value.limit.return_value.to_list = AsyncMock(
            return_value=[{"last_updated": "2024-01-01T00:00:00Z"}]
        )
        
        response = client.get("/api/v1/health/detailed")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "database" in data
        assert "api" in data


class TestProductEndpoints:
    """Test product-related endpoints."""
    
    def test_list_products(self, client, mock_db_manager):
        """Test listing products."""
        # Mock search results
        mock_products = [
            {
                "_id": "507f1f77bcf86cd799439011",
                "name": "Test Product",
                "price": {"current": 123.45},
                "market": "coto"
            }
        ]
        mock_db_manager.search_products = AsyncMock(return_value=mock_products)
        
        response = client.get("/api/v1/products?limit=10")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "Test Product"
    
    def test_search_products(self, client, mock_db_manager):
        """Test product search."""
        mock_products = [
            {
                "_id": "507f1f77bcf86cd799439011",
                "name": "Coca Cola",
                "price": {"current": 234.56},
                "market": "carrefour"
            }
        ]
        mock_db_manager.search_products = AsyncMock(return_value=mock_products)
        
        response = client.get("/api/v1/products/search?q=coca%20cola")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert "Coca Cola" in data[0]["name"]
    
    def test_get_products_by_market(self, client, mock_db_manager):
        """Test getting products by market."""
        mock_products = [
            {
                "_id": "507f1f77bcf86cd799439011",
                "name": "Jumbo Product",
                "price": {"current": 345.67},
                "market": "jumbo"
            }
        ]
        mock_db_manager.search_products = AsyncMock(return_value=mock_products)
        
        response = client.get("/api/v1/products/market/jumbo")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["market"] == "jumbo"
    
    def test_get_categories(self, client, mock_db_manager):
        """Test getting categories."""
        mock_results = [
            {"_id": "Bebidas"},
            {"_id": "Almacén"},
            {"_id": "Frescos"}
        ]
        mock_db_manager.products_collection.aggregate = AsyncMock()
        mock_db_manager.products_collection.aggregate.return_value.to_list = AsyncMock(
            return_value=mock_results
        )
        
        response = client.get("/api/v1/products/categories")
        assert response.status_code == 200
        
        data = response.json()
        assert "Bebidas" in data
        assert "Almacén" in data
    
    def test_get_brands(self, client, mock_db_manager):
        """Test getting brands."""
        mock_results = [
            {"_id": "Coca Cola"},
            {"_id": "Pepsi"},
            {"_id": "Quilmes"}
        ]
        mock_db_manager.products_collection.aggregate = AsyncMock()
        mock_db_manager.products_collection.aggregate.return_value.to_list = AsyncMock(
            return_value=mock_results
        )
        
        response = client.get("/api/v1/products/brands?market=coto")
        assert response.status_code == 200
        
        data = response.json()
        assert "Coca Cola" in data
        assert "Pepsi" in data
    
    def test_price_comparison(self, client, mock_db_manager):
        """Test price comparison."""
        mock_products = [
            {
                "name": "Coca Cola 2L",
                "price": {"current": 200.0},
                "market": "disco",
                "url": "https://disco.com/product/1",
                "availability": "in_stock"
            },
            {
                "name": "Coca Cola 2L",
                "price": {"current": 220.0},
                "market": "jumbo",
                "url": "https://jumbo.com/product/1",
                "availability": "in_stock"
            }
        ]
        mock_db_manager.search_products = AsyncMock(return_value=mock_products)
        
        response = client.get("/api/v1/products/price-comparison?product_name=coca%20cola")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 2
        # Should be sorted by price (cheapest first)
        assert data[0]["price"] <= data[1]["price"]
        assert data[0]["market"] == "disco"


class TestMarketEndpoints:
    """Test market-related endpoints."""
    
    def test_list_markets(self, client, mock_db_manager):
        """Test listing markets."""
        mock_stats = ProductStats(
            total_products=1000,
            products_by_market={"coto": 300, "carrefour": 250, "jumbo": 200, "disco": 150, "vea": 100},
            products_by_category={},
            average_price_by_market={"coto": 567.89, "carrefour": 543.21},
            last_update="2024-01-01T00:00:00Z",
            products_with_promotions=100,
            out_of_stock_products=50
        )
        mock_db_manager.get_product_stats = AsyncMock(return_value=mock_stats)
        
        response = client.get("/api/v1/markets")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 5  # All markets
        
        # Check that all markets are present
        market_names = [market["name"] for market in data]
        assert "coto" in market_names
        assert "carrefour" in market_names
    
    def test_get_market_info(self, client, mock_db_manager):
        """Test getting market information."""
        mock_stats = ProductStats(
            total_products=1000,
            products_by_market={"coto": 300},
            products_by_category={},
            average_price_by_market={"coto": 567.89},
            last_update="2024-01-01T00:00:00Z",
            products_with_promotions=50,
            out_of_stock_products=25
        )
        mock_db_manager.get_product_stats = AsyncMock(return_value=mock_stats)
        
        # Mock category aggregation
        mock_categories = [{"_id": "Bebidas", "count": 50}, {"_id": "Almacén", "count": 100}]
        mock_db_manager.products_collection.aggregate = AsyncMock()
        mock_db_manager.products_collection.aggregate.return_value.to_list = AsyncMock(
            return_value=mock_categories
        )
        
        # Mock recent products
        mock_db_manager.search_products = AsyncMock(return_value=[
            {"name": "Recent Product", "price": {"current": 123.45}}
        ])
        
        response = client.get("/api/v1/markets/coto")
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == "coto"
        assert data["product_count"] == 300
        assert "categories" in data
        assert "recent_products" in data


class TestErrorHandling:
    """Test error handling."""
    
    def test_invalid_market(self, client):
        """Test invalid market parameter."""
        response = client.get("/api/v1/products/market/invalid_market")
        assert response.status_code == 422  # Validation error
    
    def test_missing_search_query(self, client):
        """Test missing required search query."""
        response = client.get("/api/v1/products/search")
        assert response.status_code == 422  # Missing required parameter
    
    def test_invalid_pagination(self, client, mock_db_manager):
        """Test invalid pagination parameters."""
        mock_db_manager.search_products = AsyncMock(return_value=[])
        
        # Test negative skip
        response = client.get("/api/v1/products?skip=-1")
        assert response.status_code == 422
        
        # Test limit too high
        response = client.get("/api/v1/products?limit=2000")
        assert response.status_code == 422


class TestRootEndpoint:
    """Test root endpoint."""
    
    def test_root(self, client):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert data["docs"] == "/docs"


if __name__ == "__main__":
    pytest.main([__file__])
