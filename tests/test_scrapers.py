"""
Tests for scraper functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from data.models import ScrapingConfig, MarketEnum, Product, PriceInfo
from scrapers.base_scraper import BaseScraper
from scrapers.coto_scraper import CotoScraper
from scrapers.carrefour_scraper import Carrefour<PERSON>craper
from scrapers.utils import (
    extract_price_from_text,
    extract_brand_from_name,
    normalize_category,
    extract_weight_or_size,
    detect_availability_from_text
)


class TestBaseScraper:
    """Test base scraper functionality."""
    
    def setup_method(self):
        """Setup test configuration."""
        self.config = ScrapingConfig(
            rate_limit_delay=0.1,  # Fast for testing
            max_retries=2,
            timeout=10,
            headless=True,
            user_agent_rotation=False,
            markets_enabled=[MarketEnum.COTO]
        )
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        scraper = CotoScraper(self.config)
        
        import time
        start_time = time.time()
        scraper._rate_limit()
        scraper._rate_limit()
        end_time = time.time()
        
        # Should have at least one delay
        assert end_time - start_time >= self.config.rate_limit_delay
    
    def test_robots_txt_checking(self):
        """Test robots.txt compliance checking."""
        scraper = CotoScraper(self.config)
        
        # Test with a URL that should be allowed
        allowed_url = "https://www.cotodigital3.com.ar/producto/test"
        assert scraper._check_robots_txt(allowed_url) is True
        
        # Test with a potentially disallowed URL
        disallowed_url = "https://www.carrefour.com.ar/account/login"
        # This might be blocked by robots.txt
        result = scraper._check_robots_txt(disallowed_url)
        assert isinstance(result, bool)
    
    def test_price_extraction(self):
        """Test price extraction from text."""
        scraper = CotoScraper(self.config)
        
        # Test with valid price text
        price_text = "$1.234,56"
        price = scraper._extract_price(price_text)
        assert price == 1234.56
        
        # Test with invalid text
        invalid_text = "No price here"
        price = scraper._extract_price(invalid_text)
        assert price is None
    
    def test_product_name_normalization(self):
        """Test product name normalization."""
        scraper = CotoScraper(self.config)
        
        # Test with messy name
        messy_name = "  PRODUCTO:   Coca Cola   2.25L  "
        normalized = scraper._normalize_product_name(messy_name)
        assert normalized == "Coca Cola 2.25L"


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_extract_price_from_text(self):
        """Test price extraction utility."""
        # Test Argentine peso format
        assert extract_price_from_text("$1.234,56") == 1234.56
        assert extract_price_from_text("1.234,56 $") == 1234.56
        assert extract_price_from_text("$1234") == 1234.0
        assert extract_price_from_text("1234,56 pesos") == 1234.56
        
        # Test invalid formats
        assert extract_price_from_text("No price") is None
        assert extract_price_from_text("") is None
    
    def test_extract_brand_from_name(self):
        """Test brand extraction from product name."""
        assert extract_brand_from_name("COCA COLA Gaseosa 2.25L") == "COCA"
        assert extract_brand_from_name("Quilmes Cerveza 1L") == "Quilmes"
        assert extract_brand_from_name("simple product") is None
    
    def test_normalize_category(self):
        """Test category normalization."""
        assert normalize_category("lacteos") == "Lácteos"
        assert normalize_category("BEBIDAS") == "Bebidas"
        assert normalize_category("limpieza") == "Limpieza"
    
    def test_extract_weight_or_size(self):
        """Test weight/size extraction."""
        assert extract_weight_or_size("Coca Cola 2.25L") == "2.25L"
        assert extract_weight_or_size("Arroz 1kg") == "1kg"
        assert extract_weight_or_size("Pack 6 x 500ml") == "6 x 500ml"
        assert extract_weight_or_size("No size info") is None
    
    def test_detect_availability_from_text(self):
        """Test availability detection."""
        assert detect_availability_from_text("Sin stock") == "out_of_stock"
        assert detect_availability_from_text("Disponible") == "in_stock"
        assert detect_availability_from_text("Pocas unidades") == "limited_stock"
        assert detect_availability_from_text("Random text") == "unknown"


class TestCotoScraper:
    """Test Coto scraper specifically."""
    
    def setup_method(self):
        """Setup test configuration."""
        self.config = ScrapingConfig(
            rate_limit_delay=0.1,
            max_retries=1,
            timeout=10,
            headless=True,
            markets_enabled=[MarketEnum.COTO]
        )
    
    def test_scraper_initialization(self):
        """Test scraper initialization."""
        scraper = CotoScraper(self.config)
        assert scraper.market == MarketEnum.COTO
        assert scraper.base_url == "https://www.cotodigital3.com.ar"
        assert "ofertas" in scraper.categories
    
    @patch('scrapers.coto_scraper.CotoScraper._setup_driver')
    def test_get_product_urls_mock(self, mock_setup_driver):
        """Test getting product URLs with mocked driver."""
        scraper = CotoScraper(self.config)
        
        # Mock driver and elements
        mock_driver = Mock()
        mock_element = Mock()
        mock_element.get_attribute.return_value = "https://www.cotodigital3.com.ar/producto/123"
        mock_driver.find_elements.return_value = [mock_element]
        
        scraper.driver = mock_driver
        scraper._navigate_with_driver = Mock(return_value=True)
        scraper._extract_product_urls_from_page = Mock(return_value=["https://www.cotodigital3.com.ar/producto/123"])
        scraper._handle_pagination = Mock(return_value=[])
        
        urls = scraper.get_product_urls("ofertas")
        assert len(urls) >= 0  # Should return some URLs or empty list
    
    def test_extract_product_id(self):
        """Test product ID extraction from URL."""
        scraper = CotoScraper(self.config)
        
        url = "https://www.cotodigital3.com.ar/producto/123456/coca-cola"
        product_id = scraper._extract_product_id(url)
        assert product_id == "123456"
        
        # Test fallback to URL
        invalid_url = "https://www.cotodigital3.com.ar/invalid"
        product_id = scraper._extract_product_id(invalid_url)
        assert product_id == invalid_url


class TestCarrefourScraper:
    """Test Carrefour scraper specifically."""
    
    def setup_method(self):
        """Setup test configuration."""
        self.config = ScrapingConfig(
            rate_limit_delay=0.1,
            max_retries=1,
            timeout=10,
            headless=True,
            markets_enabled=[MarketEnum.CARREFOUR]
        )
    
    def test_scraper_initialization(self):
        """Test scraper initialization."""
        scraper = CarrefourScraper(self.config)
        assert scraper.market == MarketEnum.CARREFOUR
        assert scraper.base_url == "https://www.carrefour.com.ar"
        assert "almacen" in scraper.categories
    
    @patch('scrapers.carrefour_scraper.BeautifulSoup')
    @patch('scrapers.carrefour_scraper.CarrefourScraper._make_request')
    def test_extract_from_soup(self, mock_request, mock_soup):
        """Test product extraction from BeautifulSoup."""
        scraper = CarrefourScraper(self.config)
        
        # Mock HTML response
        mock_response = Mock()
        mock_response.content = "<html><body>Test</body></html>"
        mock_request.return_value = mock_response
        
        # Mock soup elements
        mock_soup_instance = Mock()
        mock_soup.return_value = mock_soup_instance
        
        # Mock product name element
        name_element = Mock()
        name_element.get_text.return_value = "Test Product"
        mock_soup_instance.select_one.side_effect = [
            name_element,  # name
            None,  # price (will cause return None)
        ]
        
        url = "https://www.carrefour.com.ar/producto/123"
        product = scraper._extract_from_soup(mock_soup_instance, url)
        
        # Should return None because price is None
        assert product is None


@pytest.mark.asyncio
class TestAsyncScraperOperations:
    """Test async scraper operations."""
    
    def setup_method(self):
        """Setup test configuration."""
        self.config = ScrapingConfig(
            rate_limit_delay=0.1,
            max_retries=1,
            timeout=10,
            headless=True,
            markets_enabled=[MarketEnum.COTO]
        )
    
    @patch('data.database.db_manager.connect')
    @patch('data.database.db_manager.save_scraping_session')
    @patch('data.database.db_manager.save_products_batch')
    async def test_run_scraping(self, mock_save_batch, mock_save_session, mock_connect):
        """Test complete scraping run."""
        scraper = CotoScraper(self.config)
        
        # Mock database operations
        mock_connect.return_value = None
        mock_save_session.return_value = True
        mock_save_batch.return_value = 5
        
        # Mock scraping methods
        scraper.scrape_products = AsyncMock(return_value=[
            Product(
                name="Test Product",
                price=PriceInfo(current=100.0),
                market=MarketEnum.COTO,
                url="https://test.com/product/1"
            )
        ])
        
        result = await scraper.run_scraping()
        assert result == 5  # Should return the mocked save count


if __name__ == "__main__":
    pytest.main([__file__])
